<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#e5e7eb" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">需求升级：从"活下来"到"活得好"</text>
  
  <!-- 副标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#6b7280">银发族想要什么？</text>
  
  <!-- 四维度框架 -->
  <text x="960" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#1e3a8a">核心四维度需求框架</text>
  
  <!-- 中心圆 -->
  <circle cx="960" cy="540" r="120" fill="#f8fafc" stroke="#cbd5e1" stroke-width="3"/>
  <text x="960" y="530" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">银发族</text>
  <text x="960" y="560" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">核心需求</text>
  
  <!-- 安全 (Safe) - 左上 -->
  <rect x="400" y="350" width="300" height="150" rx="20" fill="#fef2f2" stroke="#ef4444" stroke-width="3"/>
  <circle cx="480" cy="400" r="40" fill="#ef4444"/>
  <path d="M460,385 L480,405 L500,375" stroke="white" stroke-width="4" fill="none"/>
  <text x="530" y="390" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#dc2626">安全 Safe</text>
  <text x="530" y="420" font-family="Microsoft YaHei" font-size="28" fill="#374151">"活得安心"</text>
  <text x="420" y="450" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">防意外、防风险</text>
  <text x="420" y="475" font-family="Microsoft YaHei" font-size="24" fill="#6b7280" font-weight="bold">及时救助 (基础需求!)</text>
  
  <!-- 健康 (Healthy) - 右上 -->
  <rect x="1220" y="350" width="300" height="150" rx="20" fill="#ecfdf5" stroke="#10b981" stroke-width="3"/>
  <circle cx="1300" cy="400" r="40" fill="#10b981"/>
  <path d="M1285,385 Q1300,370 1315,385 Q1300,415 1285,385" fill="white"/>
  <text x="1350" y="390" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#065f46">健康 Healthy</text>
  <text x="1350" y="420" font-family="Microsoft YaHei" font-size="28" fill="#374151">"活得硬朗"</text>
  <text x="1240" y="450" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">慢病管理、健康促进</text>
  <text x="1240" y="475" font-family="Microsoft YaHei" font-size="24" fill="#6b7280" font-weight="bold">便捷医疗 (增长需求!)</text>
  
  <!-- 便捷 (Convenient) - 左下 -->
  <rect x="400" y="680" width="300" height="150" rx="20" fill="#f0f9ff" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="480" cy="730" r="40" fill="#3b82f6"/>
  <rect x="465" y="715" width="30" height="30" rx="5" fill="white"/>
  <circle cx="480" cy="730" r="8" fill="#3b82f6"/>
  <text x="530" y="720" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">便捷 Convenient</text>
  <text x="530" y="750" font-family="Microsoft YaHei" font-size="28" fill="#374151">"活得省心"</text>
  <text x="420" y="780" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">生活照料、智能家居</text>
  <text x="420" y="805" font-family="Microsoft YaHei" font-size="24" fill="#6b7280" font-weight="bold">信息通畅 (品质需求!)</text>
  
  <!-- 精神 (Caring) - 右下 -->
  <rect x="1220" y="680" width="300" height="150" rx="20" fill="#fef3c7" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="1300" cy="730" r="40" fill="#f59e0b"/>
  <path d="M1285,720 Q1300,705 1315,720 Q1300,745 1285,720 M1300,735 L1300,745" stroke="white" stroke-width="3" fill="none"/>
  <text x="1350" y="720" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#92400e">精神 Caring</text>
  <text x="1350" y="750" font-family="Microsoft YaHei" font-size="28" fill="#374151">"活得开心"</text>
  <text x="1240" y="780" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">情感连接、社交娱乐</text>
  <text x="1240" y="805" font-family="Microsoft YaHei" font-size="24" fill="#6b7280" font-weight="bold">价值实现 (情感需求!)</text>
  
  <!-- 连接线 -->
  <path d="M840,540 L700,425" stroke="#cbd5e1" stroke-width="2" stroke-dasharray="5,5"/>
  <path d="M1080,540 L1220,425" stroke="#cbd5e1" stroke-width="2" stroke-dasharray="5,5"/>
  <path d="M840,540 L700,755" stroke="#cbd5e1" stroke-width="2" stroke-dasharray="5,5"/>
  <path d="M1080,540 L1220,755" stroke="#cbd5e1" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- 讲解要点 -->
  <rect x="120" y="880" width="1680" height="120" rx="20" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="920" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">讲解要点</text>
  <text x="160" y="955" font-family="Microsoft YaHei" font-size="28" fill="#374151">请大家牢记这四个词！这是我们理解客户、设计方案、提炼价值点的</text>
  <text x="160" y="985" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">核心框架！现代老人及其子女对养老品质的要求越来越高。</text>
  
  <!-- 强调框 -->
  <rect x="120" y="1020" width="1680" height="50" rx="15" fill="#dc2626"/>
  <text x="960" y="1050" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">我们的方案必须围绕这四个方面来展开！</text>
  
  <!-- 装饰元素 -->
  <circle cx="200" cy="300" r="25" fill="#3b82f6" opacity="0.1"/>
  <circle cx="1720" cy="300" r="25" fill="#10b981" opacity="0.1"/>
  <circle cx="200" cy="900" r="25" fill="#f59e0b" opacity="0.1"/>
  <circle cx="1720" cy="900" r="25" fill="#ef4444" opacity="0.1"/>
</svg>
