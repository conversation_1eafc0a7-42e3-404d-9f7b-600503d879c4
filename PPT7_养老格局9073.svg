<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#e5e7eb" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">养老在哪儿？"9073"格局与智慧化趋势</text>
  
  <!-- 9073格局饼图 -->
  <text x="120" y="220" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">"9073"格局分布</text>
  
  <!-- 饼图中心 -->
  <g transform="translate(400,450)">
    <!-- 90% 居家养老 -->
    <path d="M 0,-200 A 200,200 0 1,1 -61.8,190.2 L 0,0 Z" fill="#3b82f6"/>
    <!-- 7% 社区养老 -->
    <path d="M -61.8,190.2 A 200,200 0 0,1 61.8,190.2 L 0,0 Z" fill="#10b981"/>
    <!-- 3% 机构养老 -->
    <path d="M 61.8,190.2 A 200,200 0 0,1 0,-200 L 0,0 Z" fill="#f59e0b"/>
    
    <!-- 中心文字 -->
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">养老格局</text>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">9073</text>
  </g>
  
  <!-- 图例和说明 -->
  <!-- 90% 居家养老 -->
  <rect x="120" y="680" width="30" height="30" fill="#3b82f6"/>
  <text x="170" y="705" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">90% 居家养老</text>
  <text x="170" y="735" font-family="Microsoft YaHei" font-size="24" fill="#374151">主流选择！我们的重点市场</text>
  <text x="170" y="760" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">在家中享受专业服务和智能辅助</text>
  
  <!-- 7% 社区养老 -->
  <rect x="120" y="780" width="30" height="30" fill="#10b981"/>
  <text x="170" y="805" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">7% 社区养老</text>
  <text x="170" y="835" font-family="Microsoft YaHei" font-size="24" fill="#374151">重要支撑</text>
  <text x="170" y="860" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">就近享受日间照料和专业服务</text>
  
  <!-- 3% 机构养老 -->
  <rect x="120" y="880" width="30" height="30" fill="#f59e0b"/>
  <text x="170" y="905" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">3% 机构养老</text>
  <text x="170" y="935" font-family="Microsoft YaHei" font-size="24" fill="#374151">必要补充</text>
  <text x="170" y="960" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">专业机构提供全方位照护</text>
  
  <!-- 演变趋势 -->
  <text x="800" y="220" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">智慧化演变趋势</text>
  
  <!-- 居家演变 -->
  <rect x="800" y="260" width="1000" height="120" rx="15" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
  <text x="830" y="290" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">居家养老</text>
  <path d="M1050,320 L1150,320" stroke="#374151" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="1180" y="295" font-family="Microsoft YaHei" font-size="26" fill="#374151">专业服务上门</text>
  <text x="1180" y="325" font-family="Microsoft YaHei" font-size="26" fill="#374151" font-weight="bold">+ 智能化辅助</text>
  <text x="1180" y="355" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">智能监护、健康管理、便民服务</text>
  
  <!-- 社区演变 -->
  <rect x="800" y="400" width="1000" height="120" rx="15" fill="#dcfce7" stroke="#10b981" stroke-width="2"/>
  <text x="830" y="430" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">社区养老</text>
  <path d="M1050,460 L1150,460" stroke="#374151" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="1180" y="435" font-family="Microsoft YaHei" font-size="26" fill="#374151">嵌入式服务</text>
  <text x="1180" y="465" font-family="Microsoft YaHei" font-size="26" fill="#374151" font-weight="bold">+ 平台化管理</text>
  <text x="1180" y="495" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">日间照料、康复训练、社交活动</text>
  
  <!-- 机构演变 -->
  <rect x="800" y="540" width="1000" height="120" rx="15" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
  <text x="830" y="570" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#92400e">机构养老</text>
  <path d="M1050,600 L1150,600" stroke="#374151" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="1180" y="575" font-family="Microsoft YaHei" font-size="26" fill="#374151">医养深度结合</text>
  <text x="1180" y="605" font-family="Microsoft YaHei" font-size="26" fill="#374151" font-weight="bold">+ 精细化运营</text>
  <text x="1180" y="635" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">智能床位、健康监测、护理机器人</text>
  
  <!-- 共同趋势 -->
  <rect x="800" y="700" width="1000" height="100" rx="20" fill="#dc2626"/>
  <text x="1300" y="740" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">共同趋势</text>
  <text x="1300" y="775" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">智慧化、网络化、平台化！</text>
  
  <!-- 讲解要点 -->
  <rect x="800" y="830" width="1000" height="120" rx="15" fill="#f0f9ff" stroke="#3b82f6" stroke-width="2"/>
  <text x="830" y="870" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">讲解要点</text>
  <text x="830" y="905" font-family="Microsoft YaHei" font-size="26" fill="#374151">理解这个格局，就知道我们的产品和服务应该落地在哪些场景。</text>
  <text x="830" y="935" font-family="Microsoft YaHei" font-size="26" fill="#374151" font-weight="bold">无论哪种模式，"智慧化"都是大势所趋，这正是我们电信的机会所在！</text>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>
  
  <!-- 装饰元素 -->
  <circle cx="700" cy="350" r="30" fill="#3b82f6" opacity="0.1"/>
  <circle cx="700" cy="350" r="15" fill="#3b82f6" opacity="0.2"/>
</svg>
