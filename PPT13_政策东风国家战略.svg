<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="windGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 政策东风装饰 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#3b82f6" stroke-width="4" fill="none" opacity="0.6"/>
  <path d="M0,200 Q480,150 960,200 T1920,200" stroke="#1e3a8a" stroke-width="3" fill="none" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">政策解读：智慧康养，风口已至！</text>
  
  <!-- 国家层面政策 -->
  <text x="120" y="250" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#dc2626">国家层面政策</text>
  
  <!-- 十四五规划 -->
  <rect x="120" y="290" width="1680" height="160" rx="20" fill="white" stroke="#dc2626" stroke-width="2"/>
  <circle cx="200" cy="350" r="40" fill="#dc2626"/>
  <text x="200" y="360" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">国徽</text>
  
  <text x="270" y="330" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#dc2626">"十四五"规划纲要</text>
  <text x="270" y="370" font-family="Microsoft YaHei" font-size="28" fill="#374151">构建居家社区机构相协调、医养康养相结合的养老服务体系</text>
  <text x="270" y="405" font-family="Microsoft YaHei" font-size="28" fill="#374151">发展普惠型养老服务 | 开发适老化技术和产品</text>
  <text x="270" y="435" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">推进智慧健康养老产业发展</text>
  
  <!-- 基本养老服务体系 -->
  <rect x="120" y="480" width="1680" height="160" rx="20" fill="white" stroke="#10b981" stroke-width="2"/>
  <circle cx="200" cy="540" r="40" fill="#10b981"/>
  <text x="200" y="550" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">服务</text>
  
  <text x="270" y="520" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">基本养老服务体系建设</text>
  <text x="270" y="560" font-family="Microsoft YaHei" font-size="28" fill="#374151">建立基本养老服务清单制度</text>
  <text x="270" y="595" font-family="Microsoft YaHei" font-size="28" fill="#374151">适老化改造、家庭床位、紧急救援等纳入清单</text>
  
  <rect x="1200" y="520" width="480" height="80" rx="15" fill="#10b981" opacity="0.1"/>
  <text x="1440" y="550" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">政府买单机会！</text>
  <text x="1440" y="580" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">财政支持，市场广阔</text>
  
  <!-- 地方层面政策 -->
  <text x="120" y="700" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#1e3a8a">地方层面政策</text>
  
  <rect x="120" y="740" width="800" height="120" rx="20" fill="#f0f9ff" stroke="#3b82f6" stroke-width="2"/>
  <circle cx="200" cy="780" r="30" fill="#3b82f6"/>
  <path d="M180,770 Q200,750 220,770 Q200,800 180,770" fill="white"/>
  
  <text x="260" y="770" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">务必关注本地政策！</text>
  <text x="260" y="800" font-family="Microsoft YaHei" font-size="24" fill="#374151">补贴细则 | 试点项目 | 采购计划</text>
  <text x="260" y="830" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">因地制宜，精准对接</text>
  
  <!-- 行动点 -->
  <rect x="1000" y="740" width="700" height="120" rx="20" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <circle cx="1080" cy="780" r="30" fill="#ef4444"/>
  <text x="1080" y="790" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">行动</text>
  
  <text x="1140" y="770" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#dc2626">行动点</text>
  <text x="1140" y="800" font-family="Microsoft YaHei" font-size="24" fill="#374151">主动对接民政、卫健、街道！</text>
  <text x="1140" y="830" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">建立政府关系，获取项目信息</text>
  
  <!-- 讲解要点 -->
  <rect x="120" y="890" width="1680" height="120" rx="20" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="930" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">讲解要点</text>
  <text x="160" y="965" font-family="Microsoft YaHei" font-size="28" fill="#374151">政策不仅指明了方向，更带来了实实在在的市场机会，</text>
  <text x="160" y="995" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">特别是G端（政府）和B端（承接政府项目）市场。</text>
  
  <!-- 金矿提示 -->
  <rect x="120" y="1030" width="1680" height="50" rx="15" fill="#f59e0b"/>
  <text x="960" y="1060" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">大家回去一定要研究本地政策，里面藏着金矿！</text>
  
  <!-- 装饰元素 - 政策风向标 -->
  <g transform="translate(1700,350)">
    <rect x="-5" y="0" width="10" height="100" fill="#6b7280"/>
    <path d="M0,0 L30,15 L0,30 L5,15 Z" fill="#ef4444"/>
    <circle cx="0" cy="50" r="8" fill="#3b82f6"/>
    <text x="40" y="20" font-family="Microsoft YaHei" font-size="16" fill="#374151">政策</text>
    <text x="40" y="35" font-family="Microsoft YaHei" font-size="16" fill="#374151">东风</text>
  </g>
  
  <!-- 装饰云朵 -->
  <g transform="translate(100,300)">
    <ellipse cx="0" cy="0" rx="20" ry="12" fill="#bfdbfe" opacity="0.6"/>
    <ellipse cx="15" cy="-5" rx="15" ry="10" fill="#bfdbfe" opacity="0.6"/>
    <ellipse cx="-15" cy="-3" rx="12" ry="8" fill="#bfdbfe" opacity="0.6"/>
  </g>
  
  <g transform="translate(1800,500)">
    <ellipse cx="0" cy="0" rx="25" ry="15" fill="#bfdbfe" opacity="0.4"/>
    <ellipse cx="20" cy="-8" rx="18" ry="12" fill="#bfdbfe" opacity="0.4"/>
    <ellipse cx="-18" cy="-5" rx="15" ry="10" fill="#bfdbfe" opacity="0.4"/>
  </g>
</svg>
