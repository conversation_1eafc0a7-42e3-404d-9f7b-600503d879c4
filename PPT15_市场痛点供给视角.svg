<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#7c2d12;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ea580c;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#fed7aa" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">行业之困：大家都在"难"什么？</text>
  
  <!-- 副标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#6b7280">挑战中蕴藏机会 (供给视角)</text>
  
  <!-- 困难1：产品同质化 -->
  <rect x="120" y="250" width="1680" height="120" rx="20" fill="white" stroke="#ea580c" stroke-width="2"/>
  <circle cx="200" cy="310" r="40" fill="#ea580c"/>
  <text x="200" y="320" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">同</text>
  
  <text x="270" y="290" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#7c2d12">产品功能大同小异？</text>
  <path d="M600,310 L700,310" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="730" y="290" font-family="Microsoft YaHei" font-size="28" fill="#374151">如何创新？</text>
  <text x="730" y="320" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">差异化竞争，技术创新突破</text>
  <text x="730" y="345" font-family="Microsoft YaHei" font-size="22" fill="#10b981" font-weight="bold">电信优势：5G+AI+云计算融合创新</text>
  
  <!-- 困难2：标准不统一 -->
  <rect x="120" y="390" width="1680" height="120" rx="20" fill="white" stroke="#f59e0b" stroke-width="2"/>
  <circle cx="200" cy="450" r="40" fill="#f59e0b"/>
  <text x="200" y="460" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">标</text>
  
  <text x="270" y="430" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">标准五花八门难互通？</text>
  <path d="M600,450 L700,450" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="730" y="430" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">电信平台优势！</text>
  <text x="730" y="460" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">统一接入标准，开放生态平台</text>
  <text x="730" y="485" font-family="Microsoft YaHei" font-size="22" fill="#10b981" font-weight="bold">天翼物联网平台，兼容性强</text>
  
  <!-- 困难3：服务资源整合难 -->
  <rect x="120" y="530" width="1680" height="120" rx="20" fill="white" stroke="#10b981" stroke-width="2"/>
  <circle cx="200" cy="590" r="40" fill="#10b981"/>
  <text x="200" y="600" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">服</text>
  
  <text x="270" y="570" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">线下服务资源难整合？</text>
  <path d="M600,590 L700,590" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="730" y="570" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">电信渠道/合作！</text>
  <text x="730" y="600" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">全国营业厅网络，社区合作伙伴</text>
  <text x="730" y="625" font-family="Microsoft YaHei" font-size="22" fill="#10b981" font-weight="bold">线上线下一体化服务体系</text>
  
  <!-- 困难4：专业人才缺乏 -->
  <rect x="120" y="670" width="1680" height="120" rx="20" fill="white" stroke="#8b5cf6" stroke-width="2"/>
  <circle cx="200" cy="730" r="40" fill="#8b5cf6"/>
  <text x="200" y="740" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">人</text>
  
  <text x="270" y="710" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#6b21a8">专业人才哪里找？</text>
  <path d="M600,730 L700,730" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="730" y="710" font-family="Microsoft YaHei" font-size="28" fill="#374151">共同挑战，电信需投入！</text>
  <text x="730" y="740" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">内部培养+外部引进+合作伙伴</text>
  <text x="730" y="765" font-family="Microsoft YaHei" font-size="22" fill="#ef4444" font-weight="bold">需要持续投入和体系建设</text>
  
  <!-- 困难5：商业模式探索 -->
  <rect x="120" y="810" width="1680" height="120" rx="20" fill="white" stroke="#ef4444" stroke-width="2"/>
  <circle cx="200" cy="870" r="40" fill="#ef4444"/>
  <text x="200" y="880" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">钱</text>
  
  <text x="270" y="850" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#dc2626">怎么持续赚钱？</text>
  <path d="M600,870 L700,870" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="730" y="850" font-family="Microsoft YaHei" font-size="28" fill="#374151">商业模式探索！</text>
  <text x="730" y="880" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">产品+服务+平台多元化收入</text>
  <text x="730" y="905" font-family="Microsoft YaHei" font-size="22" fill="#f59e0b" font-weight="bold">需要创新商业模式和盈利点</text>
  
  <!-- 讲解要点 -->
  <rect x="120" y="960" width="1680" height="80" rx="15" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="990" font-family="Microsoft YaHei" font-size="28" fill="#374151">让学员理解行业普遍存在的困难，</text>
  <text x="160" y="1020" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">反衬电信具备解决部分核心问题的能力和潜力。</text>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280"/>
    </marker>
  </defs>
  
  <!-- 装饰元素 -->
  <g transform="translate(1750,400)">
    <circle cx="0" cy="0" r="30" fill="#ea580c" opacity="0.1"/>
    <circle cx="0" cy="0" r="15" fill="#ea580c" opacity="0.2"/>
    <path d="M-10,-10 L10,10 M10,-10 L-10,10" stroke="#ea580c" stroke-width="3"/>
  </g>
  
  <g transform="translate(80,600)">
    <circle cx="0" cy="0" r="25" fill="#f59e0b" opacity="0.1"/>
    <circle cx="0" cy="0" r="12" fill="#f59e0b" opacity="0.2"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#f59e0b">?</text>
  </g>
</svg>
