<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fef3c7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#92400e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="elderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#fde68a" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">赋能① 长者：晚年生活新体验</text>
  
  <!-- 老年人幸福生活场景 -->
  <g transform="translate(1600,300)">
    <circle cx="0" cy="0" r="120" fill="url(#elderGradient)" stroke="#f59e0b" stroke-width="3"/>
    <circle cx="0" cy="-20" r="30" fill="#fbbf24"/>
    <circle cx="-10" cy="-30" r="4" fill="#92400e"/>
    <circle cx="10" cy="-30" r="4" fill="#92400e"/>
    <path d="M-15,-10 Q0,0 15,-10" stroke="#92400e" stroke-width="3" fill="none"/>
    <ellipse cx="0" cy="20" rx="40" ry="30" fill="#fbbf24"/>
    <rect x="-20" y="50" width="15" height="40" rx="5" fill="#fbbf24"/>
    <rect x="5" y="50" width="15" height="40" rx="5" fill="#fbbf24"/>
    <rect x="-15" y="90" width="12" height="25" rx="5" fill="#fbbf24"/>
    <rect x="3" y="90" width="12" height="25" rx="5" fill="#fbbf24"/>
    <!-- 幸福光环 -->
    <circle cx="0" cy="0" r="140" fill="none" stroke="#fbbf24" stroke-width="2" stroke-dasharray="5,5" opacity="0.5"/>
  </g>
  
  <!-- 四大价值展开 -->
  <text x="120" y="250" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#92400e">四维价值升级</text>
  
  <!-- 更安全 -->
  <rect x="120" y="280" width="700" height="150" rx="20" fill="white" stroke="#ef4444" stroke-width="3"/>
  <circle cx="200" cy="340" r="40" fill="#ef4444"/>
  <path d="M185,325 L195,335 L215,315" stroke="white" stroke-width="5" fill="none"/>
  <rect x="180" y="350" width="40" height="20" rx="5" fill="white"/>
  <circle cx="200" cy="360" r="3" fill="#ef4444"/>
  
  <text x="270" y="320" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#dc2626">更安全</text>
  <text x="270" y="360" font-family="Microsoft YaHei" font-size="28" fill="#374151">意外有人知，风险早预防</text>
  <text x="270" y="395" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">24小时智能守护，紧急情况及时响应</text>
  <text x="270" y="420" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">让每一个瞬间都有安全保障</text>
  
  <!-- 更健康 -->
  <rect x="120" y="450" width="700" height="150" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="200" cy="510" r="40" fill="#10b981"/>
  <path d="M185,495 Q200,480 215,495 Q200,525 185,495 M200,525 L200,535" stroke="white" stroke-width="5" fill="none"/>
  <rect x="180" y="490" width="40" height="40" rx="5" fill="white" opacity="0.3"/>
  <text x="200" y="515" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">健</text>
  
  <text x="270" y="490" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#065f46">更健康</text>
  <text x="270" y="530" font-family="Microsoft YaHei" font-size="28" fill="#374151">数据随时看，慢病有效管</text>
  <text x="270" y="565" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">智能健康监测，科学用药管理</text>
  <text x="270" y="590" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">让健康状况一目了然</text>
  
  <!-- 更便捷 -->
  <rect x="120" y="620" width="700" height="150" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="200" cy="680" r="40" fill="#3b82f6"/>
  <rect x="180" y="660" width="40" height="40" rx="5" fill="white"/>
  <circle cx="200" cy="680" r="15" fill="#3b82f6"/>
  <path d="M190,670 Q200,660 210,670" stroke="white" stroke-width="2" fill="none"/>
  <path d="M190,690 Q200,700 210,690" stroke="white" stroke-width="2" fill="none"/>
  
  <text x="270" y="660" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">更便捷</text>
  <text x="270" y="700" font-family="Microsoft YaHei" font-size="28" fill="#374151">语音能控制，服务送到家</text>
  <text x="270" y="735" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">智能家居控制，生活服务预约</text>
  <text x="270" y="760" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">让生活更轻松自在</text>
  
  <!-- 更温暖 -->
  <rect x="120" y="790" width="700" height="150" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="200" cy="850" r="40" fill="#f59e0b"/>
  <path d="M185,840 Q195,830 205,840 Q195,860 185,840 M205,840 Q215,830 225,840 Q215,860 205,840" fill="white"/>
  <circle cx="200" cy="850" r="25" fill="#f59e0b" opacity="0.3"/>
  
  <text x="270" y="830" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#92400e">更温暖</text>
  <text x="270" y="870" font-family="Microsoft YaHei" font-size="28" fill="#374151">亲情常连接，生活不孤单</text>
  <text x="270" y="905" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">视频通话，照片分享，远程关怀</text>
  <text x="270" y="930" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">让爱无处不在</text>
  
  <!-- 更有尊严 -->
  <rect x="120" y="960" width="1680" height="100" rx="20" fill="#92400e"/>
  <text x="960" y="1000" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="white">更有尊严！</text>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#fef3c7">让每一位长者都能优雅地老去，有尊严地生活</text>
  
  <!-- 生活场景描述 -->
  <g transform="translate(1200,400)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#92400e">智慧晚年生活</text>
    
    <rect x="-150" y="20" width="300" height="100" rx="15" fill="white" stroke="#f59e0b" stroke-width="2"/>
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">早上起床，智能床垫已记录</text>
    <text x="0" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">了一夜的睡眠质量</text>
    <text x="0" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">健康数据自动同步给子女</text>
    
    <rect x="-150" y="140" width="300" height="100" rx="15" fill="white" stroke="#10b981" stroke-width="2"/>
    <text x="0" y="170" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">测量血压后，系统提醒</text>
    <text x="0" y="195" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">该吃降压药了</text>
    <text x="0" y="220" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">用药依从性大大提升</text>
    
    <rect x="-150" y="260" width="300" height="100" rx="15" fill="white" stroke="#3b82f6" stroke-width="2"/>
    <text x="0" y="290" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">语音说"小翼，开电视"</text>
    <text x="0" y="315" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">电视自动播放喜爱的戏曲</text>
    <text x="0" y="340" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">科技让生活更简单</text>
  </g>
</svg>
