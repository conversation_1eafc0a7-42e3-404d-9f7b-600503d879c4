<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ecfdf5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#065f46;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#a7f3d0" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">"智家孝老"利器②：健康管理 (品质提升)</text>
  
  <!-- 智能健康监测 -->
  <rect x="120" y="200" width="800" height="200" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="220" cy="280" r="50" fill="#10b981"/>
  <path d="M205,265 Q220,250 235,265 Q220,295 205,265 M220,295 L220,305" stroke="white" stroke-width="5" fill="none"/>
  
  <text x="300" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#065f46">智能健康监测</text>
  <text x="300" y="290" font-family="Microsoft YaHei" font-size="28" fill="#374151">血压计/血糖仪/体重秤</text>
  <text x="300" y="325" font-family="Microsoft YaHei" font-size="26" fill="#374151">解决：数据自动上传，趋势分析</text>
  <text x="300" y="360" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#10b981">价值："健康看得见！"</text>
  <text x="300" y="385" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">告别手工记录，智能健康档案</text>
  
  <!-- 用药管理 -->
  <rect x="1000" y="200" width="800" height="200" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="1100" cy="280" r="50" fill="#3b82f6"/>
  <rect x="1080" y="260" width="40" height="40" rx="5" fill="white"/>
  <rect x="1085" y="265" width="8" height="8" rx="2" fill="#3b82f6"/>
  <rect x="1097" y="265" width="8" height="8" rx="2" fill="#3b82f6"/>
  <rect x="1109" y="265" width="8" height="8" rx="2" fill="#3b82f6"/>
  <rect x="1085" y="277" width="8" height="8" rx="2" fill="#3b82f6"/>
  <rect x="1097" y="277" width="8" height="8" rx="2" fill="#3b82f6"/>
  <rect x="1109" y="277" width="8" height="8" rx="2" fill="#3b82f6"/>
  <rect x="1085" y="289" width="8" height="8" rx="2" fill="#3b82f6"/>
  <rect x="1097" y="289" width="8" height="8" rx="2" fill="#3b82f6"/>
  <rect x="1109" y="289" width="8" height="8" rx="2" fill="#3b82f6"/>
  
  <text x="1180" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">智能用药管理</text>
  <text x="1180" y="290" font-family="Microsoft YaHei" font-size="28" fill="#374151">智能药盒/用药提醒</text>
  <text x="1180" y="325" font-family="Microsoft YaHei" font-size="26" fill="#374151">解决：忘记吃药/吃错药</text>
  <text x="1180" y="360" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#3b82f6">价值："按时按量！"</text>
  <text x="1180" y="385" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">语音/APP/短信多重提醒</text>
  
  <!-- 健康设备展示 -->
  <g transform="translate(960,450)">
    <!-- 血压计 -->
    <rect x="-200" y="-20" width="80" height="40" rx="5" fill="#10b981" opacity="0.3"/>
    <rect x="-190" y="-10" width="60" height="20" rx="3" fill="#10b981"/>
    <text x="-160" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="white">120/80</text>
    <text x="-160" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">血压计</text>
    
    <!-- 血糖仪 -->
    <rect x="-100" y="-15" width="60" height="30" rx="8" fill="#f59e0b" opacity="0.3"/>
    <rect x="-90" y="-5" width="40" height="10" rx="2" fill="#f59e0b"/>
    <text x="-70" y="2" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="white">6.5</text>
    <text x="-70" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">血糖仪</text>
    
    <!-- 体重秤 -->
    <rect x="-20" y="-10" width="40" height="20" rx="10" fill="#8b5cf6" opacity="0.3"/>
    <circle cx="0" cy="0" r="8" fill="#8b5cf6"/>
    <text x="0" y="4" text-anchor="middle" font-family="Microsoft YaHei" font-size="8" fill="white">65kg</text>
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">体重秤</text>
    
    <!-- 智能药盒 -->
    <rect x="40" y="-15" width="50" height="30" rx="5" fill="#ef4444" opacity="0.3"/>
    <rect x="45" y="-10" width="8" height="8" rx="2" fill="#ef4444"/>
    <rect x="57" y="-10" width="8" height="8" rx="2" fill="#ef4444"/>
    <rect x="69" y="-10" width="8" height="8" rx="2" fill="#ef4444"/>
    <rect x="45" y="2" width="8" height="8" rx="2" fill="#ef4444"/>
    <rect x="57" y="2" width="8" height="8" rx="2" fill="#ef4444"/>
    <rect x="69" y="2" width="8" height="8" rx="2" fill="#ef4444"/>
    <text x="65" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">智能药盒</text>
    
    <!-- 健康手环 -->
    <ellipse cx="140" cy="0" rx="25" ry="15" fill="#06b6d4" opacity="0.3"/>
    <rect x="125" y="-5" width="30" height="10" rx="5" fill="#06b6d4"/>
    <text x="140" y="2" text-anchor="middle" font-family="Microsoft YaHei" font-size="8" fill="white">♥72</text>
    <text x="140" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">健康手环</text>
  </g>
  
  <!-- 数据流转 -->
  <rect x="120" y="520" width="1680" height="150" rx="20" fill="#f8fafc" stroke="#cbd5e1" stroke-width="2"/>
  <text x="160" y="560" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">数据流转</text>
  
  <!-- 流程步骤 -->
  <g transform="translate(300,600)">
    <!-- 步骤1 -->
    <rect x="0" y="-25" width="120" height="50" rx="25" fill="#10b981"/>
    <text x="60" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">设备测量</text>
    
    <!-- 箭头1 -->
    <path d="M140,0 L180,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 步骤2 -->
    <rect x="200" y="-25" width="120" height="50" rx="25" fill="#3b82f6"/>
    <text x="260" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">自动上传</text>
    
    <!-- 箭头2 -->
    <path d="M340,0 L380,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 步骤3 -->
    <rect x="400" y="-25" width="120" height="50" rx="25" fill="#f59e0b"/>
    <text x="460" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">智能分析</text>
    
    <!-- 箭头3 -->
    <path d="M540,0 L580,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 步骤4 -->
    <rect x="600" y="-25" width="120" height="50" rx="25" fill="#8b5cf6"/>
    <text x="660" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">异常预警</text>
    
    <!-- 箭头4 -->
    <path d="M740,0 L780,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 步骤5 -->
    <rect x="800" y="-25" width="120" height="50" rx="25" fill="#ef4444"/>
    <text x="860" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">健康建议</text>
  </g>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280"/>
    </marker>
  </defs>
  
  <!-- 讲解要点 -->
  <rect x="120" y="700" width="1680" height="120" rx="20" fill="#065f46" opacity="0.1"/>
  <text x="160" y="740" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">讲解要点</text>
  <text x="160" y="775" font-family="Microsoft YaHei" font-size="28" fill="#374151">这是从"被动治疗"到"主动管理"的转变！强调数据的连续性和家人共享。</text>
  <text x="160" y="805" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">特别适合有慢病的老人，子女可以远程关注父母健康状况。</text>
  
  <!-- 核心价值 -->
  <rect x="120" y="840" width="1680" height="120" rx="20" fill="#10b981"/>
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">健康管理，科学养老</text>
  <text x="960" y="920" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="white">让数据说话，让健康可视，让管理智能</text>
  <text x="960" y="950" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#dcfce7">Health Data Speaks, Smart Care Works</text>
  
  <!-- 成功案例提示 -->
  <rect x="120" y="980" width="1680" height="80" rx="15" fill="#3b82f6"/>
  <text x="960" y="1010" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">真实案例：李大爷血压异常，系统及时提醒，</text>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">子女陪同就医，及时调整用药，避免了心脑血管意外</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(80,350)">
    <circle cx="0" cy="0" r="25" fill="#10b981" opacity="0.2"/>
    <path d="M-10,-10 Q0,-20 10,-10 Q0,0 -10,-10 M0,0 L0,15" stroke="#10b981" stroke-width="3" fill="none"/>
  </g>
  
  <g transform="translate(1840,350)">
    <circle cx="0" cy="0" r="25" fill="#3b82f6" opacity="0.2"/>
    <rect x="-12" y="-8" width="24" height="16" rx="3" fill="#3b82f6" opacity="0.4"/>
    <circle cx="0" cy="0" r="6" fill="#3b82f6"/>
  </g>
</svg>
