<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#bfdbfe" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">"智家孝老"利器③：智慧生活 (便捷体验)</text>
  
  <!-- 智能音箱 -->
  <rect x="120" y="200" width="800" height="200" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="220" cy="280" r="50" fill="#3b82f6"/>
  <circle cx="220" cy="280" r="30" fill="white"/>
  <circle cx="220" cy="280" r="15" fill="#3b82f6"/>
  <path d="M205,265 Q220,250 235,265" stroke="white" stroke-width="3" fill="none"/>
  <path d="M205,295 Q220,310 235,295" stroke="white" stroke-width="3" fill="none"/>
  
  <text x="300" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">智能音箱</text>
  <text x="300" y="290" font-family="Microsoft YaHei" font-size="28" fill="#374151">语音控制/内容播放</text>
  <text x="300" y="325" font-family="Microsoft YaHei" font-size="26" fill="#374151">解决：操作复杂，娱乐单调</text>
  <text x="300" y="360" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#3b82f6">价值："动动嘴就搞定！"</text>
  <text x="300" y="385" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">戏曲/新闻/天气/提醒</text>
  
  <!-- 智能家居 -->
  <rect x="1000" y="200" width="800" height="200" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="1100" cy="280" r="50" fill="#f59e0b"/>
  <rect x="1080" y="260" width="40" height="40" rx="5" fill="white"/>
  <circle cx="1090" cy="270" r="4" fill="#f59e0b"/>
  <circle cx="1110" cy="270" r="4" fill="#f59e0b"/>
  <circle cx="1090" cy="290" r="4" fill="#f59e0b"/>
  <circle cx="1110" cy="290" r="4" fill="#f59e0b"/>
  <rect x="1095" y="275" width="10" height="10" rx="2" fill="#f59e0b"/>
  
  <text x="1180" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#92400e">智能家居</text>
  <text x="1180" y="290" font-family="Microsoft YaHei" font-size="28" fill="#374151">灯光/窗帘/空调/电视</text>
  <text x="1180" y="325" font-family="Microsoft YaHei" font-size="26" fill="#374151">解决：起身困难，操作不便</text>
  <text x="1180" y="360" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#f59e0b">价值："躺着也能控制！"</text>
  <text x="1180" y="385" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">语音/APP/一键场景</text>
  
  <!-- 语音指令示例 -->
  <g transform="translate(960,450)">
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">常用语音指令</text>
    
    <!-- 指令气泡 -->
    <ellipse cx="-300" cy="20" rx="80" ry="25" fill="#3b82f6" opacity="0.1" stroke="#3b82f6" stroke-width="2"/>
    <text x="-300" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#1e3a8a">"小翼，开灯"</text>
    
    <ellipse cx="-100" cy="20" rx="100" ry="25" fill="#10b981" opacity="0.1" stroke="#10b981" stroke-width="2"/>
    <text x="-100" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#065f46">"播放京剧"</text>
    
    <ellipse cx="100" cy="20" rx="120" ry="25" fill="#f59e0b" opacity="0.1" stroke="#f59e0b" stroke-width="2"/>
    <text x="100" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#92400e">"明天天气怎么样"</text>
    
    <ellipse cx="300" cy="20" rx="100" ry="25" fill="#8b5cf6" opacity="0.1" stroke="#8b5cf6" stroke-width="2"/>
    <text x="300" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b21a8">"提醒我吃药"</text>
  </g>
  
  <!-- 亲情互动 -->
  <rect x="120" y="520" width="1680" height="150" rx="20" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
  <text x="160" y="560" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">亲情互动</text>
  
  <!-- 互动功能 -->
  <g transform="translate(400,600)">
    <!-- 视频通话 -->
    <rect x="0" y="-25" width="120" height="50" rx="25" fill="#3b82f6"/>
    <text x="60" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">视频通话</text>
    
    <!-- 箭头1 -->
    <path d="M140,0 L180,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 语音留言 -->
    <rect x="200" y="-25" width="120" height="50" rx="25" fill="#10b981"/>
    <text x="260" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">语音留言</text>
    
    <!-- 箭头2 -->
    <path d="M340,0 L380,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 照片分享 -->
    <rect x="400" y="-25" width="120" height="50" rx="25" fill="#f59e0b"/>
    <text x="460" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">照片分享</text>
    
    <!-- 箭头3 -->
    <path d="M540,0 L580,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 远程关怀 -->
    <rect x="600" y="-25" width="120" height="50" rx="25" fill="#ef4444"/>
    <text x="660" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">远程关怀</text>
  </g>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280"/>
    </marker>
  </defs>
  
  <!-- 讲解要点 -->
  <rect x="120" y="700" width="1680" height="120" rx="20" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="740" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">讲解要点</text>
  <text x="160" y="775" font-family="Microsoft YaHei" font-size="28" fill="#374151">这是提升生活品质的部分！要强调简单易用，特别是语音控制对老人的友好性。</text>
  <text x="160" y="805" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">亲情互动功能是情感价值的体现，能打动子女。</text>
  
  <!-- 核心价值 -->
  <rect x="120" y="840" width="1680" height="120" rx="20" fill="#3b82f6"/>
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">智慧生活，简单快乐</text>
  <text x="960" y="920" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="white">让科技服务生活，让生活更有温度</text>
  <text x="960" y="950" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#dbeafe">Smart Living, Simple Joy</text>
  
  <!-- 成功案例提示 -->
  <rect x="120" y="980" width="1680" height="80" rx="15" fill="#f59e0b"/>
  <text x="960" y="1010" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">真实案例：王奶奶学会语音控制后，每天和孙女视频通话，</text>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">生活变得丰富多彩，家人关系更加亲密</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(80,350)">
    <circle cx="0" cy="0" r="25" fill="#3b82f6" opacity="0.2"/>
    <circle cx="0" cy="0" r="15" fill="#3b82f6" opacity="0.3"/>
    <path d="M-8,-8 Q0,-15 8,-8" stroke="#3b82f6" stroke-width="2" fill="none"/>
    <path d="M-8,8 Q0,15 8,8" stroke="#3b82f6" stroke-width="2" fill="none"/>
  </g>
  
  <g transform="translate(1840,350)">
    <circle cx="0" cy="0" r="25" fill="#f59e0b" opacity="0.2"/>
    <rect x="-10" y="-10" width="20" height="20" rx="3" fill="#f59e0b" opacity="0.4"/>
    <circle cx="-5" cy="-5" r="2" fill="#f59e0b"/>
    <circle cx="5" cy="-5" r="2" fill="#f59e0b"/>
    <circle cx="-5" cy="5" r="2" fill="#f59e0b"/>
    <circle cx="5" cy="5" r="2" fill="#f59e0b"/>
  </g>
</svg>
