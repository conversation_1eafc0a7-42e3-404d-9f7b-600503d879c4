<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#e5e7eb" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">应用拓展：让社区服务更有"智慧"含量</text>
  
  <!-- 赋能日照中心 -->
  <rect x="120" y="200" width="1680" height="200" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="220" cy="280" r="50" fill="#10b981"/>
  <rect x="195" y="255" width="50" height="50" rx="8" fill="white"/>
  <rect x="205" y="265" width="30" height="30" rx="5" fill="#10b981"/>
  <circle cx="215" cy="275" r="3" fill="white"/>
  <circle cx="225" cy="275" r="3" fill="white"/>
  <circle cx="215" cy="285" r="3" fill="white"/>
  <circle cx="225" cy="285" r="3" fill="white"/>
  
  <text x="300" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#065f46">赋能日照中心</text>
  <text x="300" y="290" font-family="Microsoft YaHei" font-size="28" fill="#374151">智能门禁考勤 | 环境监测（温湿度/空气质量）</text>
  <text x="300" y="325" font-family="Microsoft YaHei" font-size="28" fill="#374151">视频AI看护（活动区域）| 健康小屋（自助检测设备集成）</text>
  <text x="300" y="365" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">让日间照料更安全、更智能、更贴心</text>
  
  <!-- 优化上门服务 -->
  <rect x="120" y="420" width="1680" height="200" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="220" cy="500" r="50" fill="#3b82f6"/>
  <circle cx="220" cy="500" r="30" fill="white"/>
  <circle cx="220" cy="500" r="15" fill="#3b82f6"/>
  <path d="M210,490 L230,490 M220,480 L220,500 L230,510" stroke="white" stroke-width="3" fill="none"/>
  
  <text x="300" y="470" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">优化上门服务</text>
  <text x="300" y="510" font-family="Microsoft YaHei" font-size="28" fill="#374151">基于LBS的智能派单 | 服务人员定位与轨迹管理</text>
  <text x="300" y="545" font-family="Microsoft YaHei" font-size="28" fill="#374151">服务打卡与评价</text>
  <text x="300" y="585" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">让上门服务更高效、更透明、更可控</text>
  
  <!-- 打通医养结合 -->
  <rect x="120" y="640" width="1680" height="200" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="220" cy="720" r="50" fill="#f59e0b"/>
  <path d="M205,705 Q220,690 235,705 Q220,735 205,705 M220,735 L220,745" stroke="white" stroke-width="5" fill="none"/>
  <rect x="200" y="700" width="40" height="40" rx="5" fill="white" opacity="0.3"/>
  <text x="220" y="725" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">医</text>
  
  <text x="300" y="690" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#92400e">打通医养结合</text>
  <text x="300" y="730" font-family="Microsoft YaHei" font-size="28" fill="#374151">社区健康小屋数据接入 + 对接上级医院/互联网医院</text>
  <text x="300" y="765" font-family="Microsoft YaHei" font-size="28" fill="#374151">实现远程咨询、慢病随访</text>
  <text x="300" y="805" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">让医疗服务更便民、更专业、更连续</text>
  
  <!-- 智慧应用场景 -->
  <g transform="translate(960,880)">
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">智慧应用场景</text>
    
    <!-- 门禁考勤 -->
    <rect x="-400" y="0" width="150" height="80" rx="10" fill="#10b981" opacity="0.1" stroke="#10b981" stroke-width="2"/>
    <text x="-325" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#065f46">智能门禁</text>
    <text x="-325" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">人脸识别</text>
    <text x="-325" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">自动考勤</text>
    <text x="-325" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">异常预警</text>
    
    <!-- 环境监测 -->
    <rect x="-220" y="0" width="150" height="80" rx="10" fill="#3b82f6" opacity="0.1" stroke="#3b82f6" stroke-width="2"/>
    <text x="-145" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#1e3a8a">环境监测</text>
    <text x="-145" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">温湿度</text>
    <text x="-145" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">空气质量</text>
    <text x="-145" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">实时监控</text>
    
    <!-- AI看护 -->
    <rect x="-40" y="0" width="150" height="80" rx="10" fill="#f59e0b" opacity="0.1" stroke="#f59e0b" stroke-width="2"/>
    <text x="35" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#92400e">AI看护</text>
    <text x="35" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">行为识别</text>
    <text x="35" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">异常检测</text>
    <text x="35" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">主动预警</text>
    
    <!-- 健康小屋 -->
    <rect x="140" y="0" width="150" height="80" rx="10" fill="#8b5cf6" opacity="0.1" stroke="#8b5cf6" stroke-width="2"/>
    <text x="215" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#6b21a8">健康小屋</text>
    <text x="215" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">自助检测</text>
    <text x="215" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">数据上传</text>
    <text x="215" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">健康档案</text>
    
    <!-- 智能派单 -->
    <rect x="320" y="0" width="150" height="80" rx="10" fill="#ef4444" opacity="0.1" stroke="#ef4444" stroke-width="2"/>
    <text x="395" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#dc2626">智能派单</text>
    <text x="395" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">LBS定位</text>
    <text x="395" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">就近派单</text>
    <text x="395" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">轨迹跟踪</text>
  </g>
  
  <!-- 讲解要点 -->
  <rect x="120" y="980" width="1680" height="90" rx="15" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="1015" font-family="Microsoft YaHei" font-size="28" fill="#374151">展示平台如何与具体场景结合，提升服务能力和管理水平。</text>
  <text x="160" y="1045" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">从"人工管理"到"智能管理"，从"被动服务"到"主动服务"。</text>
</svg>
