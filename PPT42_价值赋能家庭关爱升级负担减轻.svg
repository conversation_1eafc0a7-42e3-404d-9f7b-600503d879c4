<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ecfdf5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#065f46;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="familyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#a7f3d0" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">赋能② 家庭：关爱升级，负担减轻</text>
  
  <!-- 子女放心工作/生活的场景图 -->
  <g transform="translate(1600,350)">
    <circle cx="0" cy="0" r="120" fill="url(#familyGradient)" stroke="#10b981" stroke-width="3"/>
    <!-- 子女形象 -->
    <circle cx="0" cy="-20" r="25" fill="#10b981"/>
    <circle cx="-8" cy="-28" r="3" fill="white"/>
    <circle cx="8" cy="-28" r="3" fill="white"/>
    <path d="M-10,-12 Q0,-8 10,-12" stroke="white" stroke-width="2" fill="none"/>
    <ellipse cx="0" cy="15" rx="30" ry="25" fill="#10b981"/>
    <rect x="-15" y="40" width="12" height="35" rx="5" fill="#10b981"/>
    <rect x="3" y="40" width="12" height="35" rx="5" fill="#10b981"/>
    <rect x="-12" y="75" width="10" height="20" rx="5" fill="#10b981"/>
    <rect x="2" y="75" width="10" height="20" rx="5" fill="#10b981"/>
    <!-- 手机 -->
    <rect x="20" y="-5" width="15" height="25" rx="3" fill="#3b82f6"/>
    <circle cx="27" cy="2" r="2" fill="white"/>
    <!-- 安心光环 -->
    <circle cx="0" cy="0" r="140" fill="none" stroke="#10b981" stroke-width="2" stroke-dasharray="5,5" opacity="0.5"/>
    <text x="0" y="130" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#065f46">安心工作</text>
  </g>
  
  <!-- 围绕子女痛点展开 -->
  <text x="120" y="250" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#065f46">子女四大关爱升级</text>
  
  <!-- 更省心 -->
  <rect x="120" y="280" width="700" height="150" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="200" cy="340" r="40" fill="#3b82f6"/>
  <rect x="180" y="320" width="40" height="40" rx="5" fill="white"/>
  <circle cx="200" cy="340" r="15" fill="#3b82f6"/>
  <path d="M190,330 Q200,320 210,330" stroke="white" stroke-width="2" fill="none"/>
  <path d="M190,350 Q200,360 210,350" stroke="white" stroke-width="2" fill="none"/>
  
  <text x="270" y="320" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">更省心</text>
  <text x="270" y="360" font-family="Microsoft YaHei" font-size="28" fill="#374151">远程了解状况，减少奔波</text>
  <text x="270" y="395" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">手机APP实时查看父母健康数据</text>
  <text x="270" y="420" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">不用频繁往返，工作生活两不误</text>
  
  <!-- 更放心 -->
  <rect x="120" y="450" width="700" height="150" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="200" cy="510" r="40" fill="#10b981"/>
  <path d="M185,495 L195,505 L215,485" stroke="white" stroke-width="5" fill="none"/>
  <rect x="180" y="520" width="40" height="20" rx="5" fill="white"/>
  <circle cx="200" cy="530" r="3" fill="#10b981"/>
  
  <text x="270" y="490" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#065f46">更放心</text>
  <text x="270" y="530" font-family="Microsoft YaHei" font-size="28" fill="#374151">安全/健康实时掌握，异常及时提醒</text>
  <text x="270" y="565" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">24小时智能监护，紧急情况立即通知</text>
  <text x="270" y="590" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">再也不用担心父母独自在家</text>
  
  <!-- 更安心 -->
  <rect x="120" y="620" width="700" height="150" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="200" cy="680" r="40" fill="#f59e0b"/>
  <circle cx="200" cy="680" r="25" fill="white"/>
  <circle cx="200" cy="680" r="12" fill="#f59e0b"/>
  <path d="M190,670 L210,670 M200,660 L200,680 L210,690" stroke="white" stroke-width="3" fill="none"/>
  
  <text x="270" y="660" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#92400e">更安心</text>
  <text x="270" y="700" font-family="Microsoft YaHei" font-size="28" fill="#374151">专业服务响应，减轻照护压力和焦虑</text>
  <text x="270" y="735" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">专业团队7x24小时待命</text>
  <text x="270" y="760" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">有问题第一时间专业处理</text>
  
  <!-- 更暖心 -->
  <rect x="120" y="790" width="700" height="150" rx="20" fill="white" stroke="#ef4444" stroke-width="3"/>
  <circle cx="200" cy="850" r="40" fill="#ef4444"/>
  <path d="M185,840 Q195,830 205,840 Q195,860 185,840 M205,840 Q215,830 225,840 Q215,860 205,840" fill="white"/>
  <circle cx="200" cy="850" r="25" fill="#ef4444" opacity="0.3"/>
  
  <text x="270" y="830" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#dc2626">更暖心</text>
  <text x="270" y="870" font-family="Microsoft YaHei" font-size="28" fill="#374151">亲情互动更便捷</text>
  <text x="270" y="905" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">高清视频通话，照片实时分享</text>
  <text x="270" y="930" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">让距离不再是问题</text>
  
  <!-- 子女生活场景描述 -->
  <g transform="translate(1200,450)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">子女安心生活</text>
    
    <rect x="-150" y="20" width="300" height="100" rx="15" fill="white" stroke="#3b82f6" stroke-width="2"/>
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">上班路上，手机收到</text>
    <text x="0" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">"爸爸血压正常"的推送</text>
    <text x="0" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">一天的工作都很踏实</text>
    
    <rect x="-150" y="140" width="300" height="100" rx="15" fill="white" stroke="#10b981" stroke-width="2"/>
    <text x="0" y="170" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">出差在外，突然收到</text>
    <text x="0" y="195" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">紧急报警，立即联系</text>
    <text x="0" y="220" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">专业团队已在路上</text>
    
    <rect x="-150" y="260" width="300" height="100" rx="15" fill="white" stroke="#ef4444" stroke-width="2"/>
    <text x="0" y="290" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">晚上视频通话</text>
    <text x="0" y="315" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">看到父母精神很好</text>
    <text x="0" y="340" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">内心的愧疚感减少了</text>
  </g>
  
  <!-- 讲解要点 -->
  <rect x="120" y="960" width="1680" height="100" rx="20" fill="#065f46" opacity="0.1"/>
  <text x="160" y="995" font-family="Microsoft YaHei" font-size="28" fill="#374151">这是打动子女的关键！我们的方案是帮助他们更好地尽孝，</text>
  <text x="160" y="1025" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">解决他们的实际困难。</text>
  <text x="160" y="1050" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">让孝心有了科技的翅膀</text>
</svg>
