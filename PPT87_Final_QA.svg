<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#bfdbfe" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)">答疑解惑，轻装上阵！</text>
  
  <!-- 大问号图标 -->
  <g transform="translate(960,500)">
    <circle cx="0" cy="0" r="200" fill="#3b82f6" opacity="0.1"/>
    <circle cx="0" cy="0" r="150" fill="#3b82f6" opacity="0.2"/>
    <circle cx="0" cy="0" r="100" fill="#3b82f6"/>
    
    <!-- 问号 -->
    <path d="M-30,-50 Q-30,-80 0,-80 Q30,-80 30,-50 Q30,-20 0,-20 L0,0" stroke="white" stroke-width="12" fill="none" stroke-linecap="round"/>
    <circle cx="0" cy="30" r="8" fill="white"/>
    
    <!-- 装饰问号 -->
    <g transform="translate(-120,-80)">
      <circle cx="0" cy="0" r="25" fill="#10b981" opacity="0.7"/>
      <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">?</text>
    </g>
    
    <g transform="translate(120,-80)">
      <circle cx="0" cy="0" r="25" fill="#f59e0b" opacity="0.7"/>
      <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">?</text>
    </g>
    
    <g transform="translate(-150,50)">
      <circle cx="0" cy="0" r="20" fill="#ef4444" opacity="0.7"/>
      <text x="0" y="6" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">?</text>
    </g>
    
    <g transform="translate(150,50)">
      <circle cx="0" cy="0" r="20" fill="#8b5cf6" opacity="0.7"/>
      <text x="0" y="6" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">?</text>
    </g>
  </g>
  
  <!-- 主要内容 -->
  <rect x="120" y="750" width="1680" height="200" rx="30" fill="white" stroke="#3b82f6" stroke-width="4"/>
  <text x="960" y="820" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#1e3a8a">"关于今天的所有内容，或者对接下来的实战安排，</text>
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#1e3a8a">大家还有任何疑问吗？请提出来！"</text>
  <text x="960" y="930" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#6b7280">Questions & Answers Session</text>
  
  <!-- 互动提示 -->
  <g transform="translate(960,1000)">
    <rect x="-300" y="0" width="150" height="60" rx="15" fill="#10b981" opacity="0.1" stroke="#10b981" stroke-width="2"/>
    <text x="-225" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#065f46">理论疑问</text>
    <text x="-225" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">概念澄清</text>
    
    <rect x="-120" y="0" width="150" height="60" rx="15" fill="#3b82f6" opacity="0.1" stroke="#3b82f6" stroke-width="2"/>
    <text x="-45" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#1e3a8a">工具使用</text>
    <text x="-45" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">操作指导</text>
    
    <rect x="60" y="0" width="150" height="60" rx="15" fill="#f59e0b" opacity="0.1" stroke="#f59e0b" stroke-width="2"/>
    <text x="135" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#92400e">实战安排</text>
    <text x="135" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">流程确认</text>
    
    <rect x="240" y="0" width="150" height="60" rx="15" fill="#ef4444" opacity="0.1" stroke="#ef4444" stroke-width="2"/>
    <text x="315" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#dc2626">其他问题</text>
    <text x="315" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">开放讨论</text>
  </g>
</svg>
