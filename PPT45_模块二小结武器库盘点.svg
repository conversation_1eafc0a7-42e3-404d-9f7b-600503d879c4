<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#e5e7eb" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">"武器库"盘点：心中有数，手中有器</text>
  
  <!-- 小结内容 -->
  <text x="960" y="220" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#6b7280">模块二小结 &amp; Q&amp;A</text>
  
  <!-- 简要回顾 -->
  <text x="120" y="300" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#1e3a8a">简要回顾</text>
  
  <!-- 三大场景方案 -->
  <rect x="120" y="330" width="1680" height="120" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="200" cy="380" r="40" fill="#10b981"/>
  <text x="200" y="390" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">场景</text>
  
  <text x="280" y="365" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">三大场景方案，满足不同需求</text>
  <text x="280" y="405" font-family="Microsoft YaHei" font-size="28" fill="#374151">居家养老 | 社区养老 | 机构养老</text>
  <text x="280" y="430" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">全场景覆盖，精准匹配客户需求</text>
  
  <!-- 四维价值 -->
  <rect x="120" y="470" width="1680" height="120" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="200" cy="520" r="40" fill="#3b82f6"/>
  <text x="200" y="530" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">价值</text>
  
  <text x="280" y="505" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">围绕"四维价值"，传递核心利益</text>
  <text x="280" y="545" font-family="Microsoft YaHei" font-size="28" fill="#374151">安全 | 健康 | 便捷 | 温暖</text>
  <text x="280" y="570" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">让每一个价值点都能打动客户</text>
  
  <!-- 技术实力 -->
  <rect x="120" y="610" width="1680" height="120" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="200" cy="660" r="40" fill="#f59e0b"/>
  <text x="200" y="670" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">技术</text>
  
  <text x="280" y="645" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">技术实力是根基，服务体系是保障</text>
  <text x="280" y="685" font-family="Microsoft YaHei" font-size="28" fill="#374151">云网融合 | 物联接入 | 智能分析 | 安全保障</text>
  <text x="280" y="710" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">电信级技术底座，值得信赖</text>
  
  <!-- 赋能理念 -->
  <rect x="120" y="750" width="1680" height="120" rx="20" fill="white" stroke="#ef4444" stroke-width="3"/>
  <circle cx="200" cy="800" r="40" fill="#ef4444"/>
  <text x="200" y="810" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">赋能</text>
  
  <text x="280" y="785" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#dc2626">核心是"赋能"，为各方创造价值</text>
  <text x="280" y="825" font-family="Microsoft YaHei" font-size="28" fill="#374151">长者 | 家庭 | 机构 | 社会</text>
  <text x="280" y="850" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">让每一方都能从中受益</text>
  
  <!-- 互动环节 -->
  <rect x="120" y="890" width="1680" height="120" rx="20" fill="#f8fafc" stroke="#cbd5e1" stroke-width="2"/>
  <text x="160" y="930" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">互动环节</text>
  <text x="160" y="970" font-family="Microsoft YaHei" font-size="32" fill="#374151">"关于我们的产品和服务，大家还有哪些不清楚的地方？"</text>
  <text x="160" y="995" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">预留5-10分钟进行Q&amp;A互动</text>
  
  <!-- 过渡提示 -->
  <rect x="120" y="1030" width="1680" height="40" rx="15" fill="#10b981"/>
  <text x="960" y="1055" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">武器库盘点完毕！接下来学习如何精准使用这些武器 → 模块三：客户分析与需求挖掘</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(100,400)">
    <circle cx="0" cy="0" r="25" fill="#10b981" opacity="0.2"/>
    <rect x="-12" y="-12" width="24" height="24" rx="3" fill="#10b981" opacity="0.4"/>
    <circle cx="0" cy="0" r="6" fill="#10b981"/>
  </g>
  
  <g transform="translate(1820,600)">
    <circle cx="0" cy="0" r="25" fill="#3b82f6" opacity="0.2"/>
    <path d="M-12,-12 L0,0 L12,-12 M0,0 L0,15" stroke="#3b82f6" stroke-width="3" fill="none"/>
  </g>
  
  <!-- 武器图标装饰 -->
  <g transform="translate(1750,300)">
    <circle cx="0" cy="0" r="30" fill="#ef4444" opacity="0.2"/>
    <rect x="-18" y="-3" width="36" height="6" rx="3" fill="#ef4444"/>
    <circle cx="-21" cy="0" r="6" fill="#ef4444"/>
    <circle cx="21" cy="0" r="6" fill="#ef4444"/>
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#dc2626">武器库</text>
  </g>
</svg>
