<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#e5e7eb" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)">欢迎加入实战营！逐浪蓝海，智赢未来！</text>
  
  <!-- 欢迎语区域 -->
  <rect x="120" y="220" width="1680" height="320" rx="20" fill="white" stroke="#e5e7eb" stroke-width="2"/>
  <text x="160" y="270" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">欢迎语</text>
  
  <text x="160" y="320" font-family="Microsoft YaHei" font-size="28" fill="#374151">各位伙伴，大家上午好！非常荣幸和大家相聚在这里，共同开启为期四天的</text>
  <text x="160" y="360" font-family="Microsoft YaHei" font-size="28" fill="#374151">《智慧领航，银龄共赢》实战营！我是今天的讲师XXX，在相关领域有XX年的</text>
  <text x="160" y="400" font-family="Microsoft YaHei" font-size="28" fill="#374151">实践经验，也非常关注智慧康养这个充满潜力的领域。</text>
  
  <text x="160" y="460" font-family="Microsoft YaHei" font-size="28" fill="#374151">未来四天，期待和大家一起，既仰望星空（看市场、看趋势），</text>
  <text x="160" y="500" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">更脚踏实地（练技能、促实战）！</text>
  
  <!-- 破冰互动区域 -->
  <rect x="120" y="580" width="1680" height="420" rx="20" fill="#f0f9ff" stroke="#3b82f6" stroke-width="2"/>
  <text x="160" y="630" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">破冰互动</text>
  
  <text x="160" y="690" font-family="Microsoft YaHei" font-size="30" fill="#374151">在正式开始前，我们先来做个小互动。请大家快速思考一下：</text>
  
  <rect x="200" y="720" width="1520" height="80" rx="15" fill="#3b82f6" opacity="0.1"/>
  <text x="960" y="770" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">提到"智慧康养"，您脑海中首先蹦出的1-2个关键词是什么？</text>
  
  <text x="160" y="840" font-family="Microsoft YaHei" font-size="28" fill="#374151">请写在发给大家的便利贴上，然后我们一起分享讨论！</text>
  
  <!-- 关键词示例 -->
  <rect x="200" y="870" width="120" height="60" rx="10" fill="#fbbf24"/>
  <text x="260" y="905" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">老人</text>
  
  <rect x="340" y="870" width="120" height="60" rx="10" fill="#10b981"/>
  <text x="400" y="905" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">科技</text>
  
  <rect x="480" y="870" width="120" height="60" rx="10" fill="#f59e0b"/>
  <text x="540" y="905" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">安全</text>
  
  <rect x="620" y="870" width="120" height="60" rx="10" fill="#8b5cf6"/>
  <text x="680" y="905" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">健康</text>
  
  <rect x="760" y="870" width="120" height="60" rx="10" fill="#ef4444"/>
  <text x="820" y="905" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">APP</text>
  
  <text x="920" y="905" font-family="Microsoft YaHei" font-size="28" fill="#6b7280">...</text>
  
  <!-- 装饰元素 -->
  <circle cx="1700" cy="400" r="60" fill="#3b82f6" opacity="0.1"/>
  <path d="M1680,380 Q1700,360 1720,380 Q1700,400 1680,380" fill="#3b82f6" opacity="0.3"/>
</svg>
