<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#e5e7eb" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">智能硬件"组合拳"：提升安全与效率</text>
  
  <!-- 人员定位 -->
  <rect x="120" y="200" width="800" height="180" rx="20" fill="white" stroke="#8b5cf6" stroke-width="3"/>
  <circle cx="220" cy="270" r="40" fill="#8b5cf6"/>
  <circle cx="220" cy="270" r="25" fill="white"/>
  <circle cx="220" cy="270" r="12" fill="#8b5cf6"/>
  <path d="M210,260 L230,260 M210,270 L230,270 M210,280 L230,280" stroke="white" stroke-width="2"/>
  
  <text x="300" y="240" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#6b21a8">人员定位</text>
  <text x="300" y="275" font-family="Microsoft YaHei" font-size="26" fill="#374151">手环/胸卡+UWB/蓝牙</text>
  <text x="300" y="305" font-family="Microsoft YaHei" font-size="24" fill="#374151">解决：防走失、难查找</text>
  <text x="300" y="340" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#8b5cf6">价值："精准定位，一键查找，电子围栏更安心"</text>
  <text x="300" y="365" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">实时位置追踪，异常区域预警</text>
  
  <!-- 生命体征监测 -->
  <rect x="1000" y="200" width="800" height="180" rx="20" fill="white" stroke="#ef4444" stroke-width="3"/>
  <circle cx="1100" cy="270" r="40" fill="#ef4444"/>
  <rect x="1080" y="250" width="40" height="40" rx="5" fill="white"/>
  <path d="M1085,265 L1095,255 L1105,275 L1115,255" stroke="#ef4444" stroke-width="3" fill="none"/>
  <circle cx="1100" cy="280" r="3" fill="#ef4444"/>
  
  <text x="1180" y="230" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#dc2626">生命体征监测</text>
  <text x="1180" y="260" font-family="Microsoft YaHei" font-size="26" fill="#374151">智能床垫/毫米波雷达</text>
  <text x="1180" y="285" font-family="Microsoft YaHei" font-size="24" fill="#374151">解决：夜巡负担重、风险高</text>
  <text x="1180" y="315" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ef4444">价值："无感监测，异常预警"</text>
  <text x="1180" y="340" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#dc2626">极大降低夜间风险，解放人力！</text>
  <text x="1180" y="365" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">(核心卖点，重点讲透)</text>
  
  <!-- 智能呼叫对讲 -->
  <rect x="120" y="400" width="800" height="180" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="220" cy="470" r="40" fill="#10b981"/>
  <circle cx="220" cy="470" r="25" fill="white"/>
  <rect x="210" y="460" width="20" height="20" rx="3" fill="#10b981"/>
  <circle cx="220" cy="470" r="6" fill="white"/>
  <path d="M205,455 Q220,445 235,455" stroke="#10b981" stroke-width="2" fill="none"/>
  
  <text x="300" y="440" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">智能呼叫对讲</text>
  <text x="300" y="475" font-family="Microsoft YaHei" font-size="26" fill="#374151">床头呼叫+双向对讲</text>
  <text x="300" y="505" font-family="Microsoft YaHei" font-size="24" fill="#374151">解决：紧急呼叫响应慢</text>
  <text x="300" y="540" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#10b981">价值："快速响应，双向沟通"</text>
  <text x="300" y="565" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">一键呼叫，护理站即时响应</text>
  
  <!-- 视频AI分析 -->
  <rect x="1000" y="400" width="800" height="180" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="1100" cy="470" r="40" fill="#3b82f6"/>
  <rect x="1080" y="450" width="40" height="40" rx="5" fill="white"/>
  <circle cx="1090" cy="460" r="4" fill="#3b82f6"/>
  <circle cx="1110" cy="460" r="4" fill="#3b82f6"/>
  <rect x="1085" y="470" width="30" height="15" rx="2" fill="#3b82f6"/>
  <circle cx="1100" cy="477" r="2" fill="white"/>
  
  <text x="1180" y="440" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">视频AI分析</text>
  <text x="1180" y="475" font-family="Microsoft YaHei" font-size="26" fill="#374151">公共区域智能监控</text>
  <text x="1180" y="505" font-family="Microsoft YaHei" font-size="24" fill="#374151">解决：入侵/徘徊/聚集等风险</text>
  <text x="1180" y="540" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#3b82f6">价值："智能识别，主动预警"</text>
  <text x="1180" y="565" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">AI算法识别异常行为</text>
  
  <!-- 核心卖点强调 -->
  <rect x="120" y="600" width="1680" height="150" rx="20" fill="#fef2f2" stroke="#ef4444" stroke-width="3"/>
  <text x="160" y="640" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#dc2626">核心卖点：生命体征无感监测</text>
  <text x="160" y="680" font-family="Microsoft YaHei" font-size="32" fill="#374151">算一笔账：能替代多少夜班人力？能避免多少意外风险？</text>
  <text x="160" y="715" font-family="Microsoft YaHei" font-size="32" fill="#374151" font-weight="bold">这对院长非常有吸引力！</text>
  <text x="160" y="740" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">24小时无感监测，异常自动预警，大幅降低夜间巡查工作量</text>
  
  <!-- 硬件组合效果 -->
  <g transform="translate(960,850)">
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">硬件组合效果</text>
    
    <!-- 定位手环 -->
    <rect x="-400" y="0" width="150" height="80" rx="10" fill="#8b5cf6" opacity="0.1" stroke="#8b5cf6" stroke-width="2"/>
    <text x="-325" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#6b21a8">定位手环</text>
    <text x="-325" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">实时位置</text>
    <text x="-325" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">电子围栏</text>
    <text x="-325" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">防走失</text>
    
    <!-- 智能床垫 -->
    <rect x="-220" y="0" width="150" height="80" rx="10" fill="#ef4444" opacity="0.1" stroke="#ef4444" stroke-width="2"/>
    <text x="-145" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#dc2626">智能床垫</text>
    <text x="-145" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">心率呼吸</text>
    <text x="-145" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">离床检测</text>
    <text x="-145" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">无感监测</text>
    
    <!-- 呼叫器 -->
    <rect x="-40" y="0" width="150" height="80" rx="10" fill="#10b981" opacity="0.1" stroke="#10b981" stroke-width="2"/>
    <text x="35" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#065f46">呼叫器</text>
    <text x="35" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">一键呼叫</text>
    <text x="35" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">双向对讲</text>
    <text x="35" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">快速响应</text>
    
    <!-- AI摄像头 -->
    <rect x="140" y="0" width="150" height="80" rx="10" fill="#3b82f6" opacity="0.1" stroke="#3b82f6" stroke-width="2"/>
    <text x="215" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#1e3a8a">AI摄像头</text>
    <text x="215" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">行为识别</text>
    <text x="215" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">异常预警</text>
    <text x="215" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">智能分析</text>
  </g>
  
  <!-- 讲解要点 -->
  <rect x="120" y="950" width="1680" height="120" rx="20" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="990" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">讲解要点</text>
  <text x="160" y="1025" font-family="Microsoft YaHei" font-size="28" fill="#374151">生命体征无感监测是机构场景的核心价值点之一！</text>
  <text x="160" y="1055" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">算一笔账：能替代多少夜班人力？能避免多少意外风险？这对院长非常有吸引力！</text>
</svg>
