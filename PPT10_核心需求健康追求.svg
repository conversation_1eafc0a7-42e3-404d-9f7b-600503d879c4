<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ecfdf5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#065f46;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#a7f3d0" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">核心需求聚焦二：健康 - 主动管理，科学养老</text>
  
  <!-- 痛点场景展示 -->
  <text x="120" y="240" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#065f46">典型痛点场景</text>
  
  <!-- 场景1：数据管理难 -->
  <rect x="120" y="280" width="550" height="200" rx="20" fill="white" stroke="#10b981" stroke-width="2"/>
  <text x="140" y="320" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">慢病数据难管理</text>
  
  <!-- 对比图：传统记录本 vs 自动上传 -->
  <g transform="translate(200,350)">
    <!-- 传统记录本 -->
    <rect x="0" y="0" width="80" height="60" rx="5" fill="#f3f4f6" stroke="#6b7280" stroke-width="2"/>
    <text x="40" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#374151">血压记录</text>
    <text x="10" y="35" font-family="Microsoft YaHei" font-size="10" fill="#6b7280">3/15 140/90</text>
    <text x="10" y="45" font-family="Microsoft YaHei" font-size="10" fill="#6b7280">3/16 ???</text>
    <text x="10" y="55" font-family="Microsoft YaHei" font-size="10" fill="#6b7280">3/17 忘记</text>
    
    <!-- VS -->
    <text x="120" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ef4444">VS</text>
    
    <!-- 自动上传图表 -->
    <rect x="160" y="0" width="120" height="60" rx="5" fill="#10b981" opacity="0.1"/>
    <path d="M170,50 L180,40 L190,45 L200,35 L210,30 L220,25 L230,20 L240,15 L250,10 L260,15" stroke="#10b981" stroke-width="3" fill="none"/>
    <text x="215" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#065f46">自动图表</text>
  </g>
  
  <text x="140" y="420" font-family="Microsoft YaHei" font-size="24" fill="#374151">血压血糖记录繁琐，数据容易丢失</text>
  <text x="140" y="450" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">无法形成有效的健康档案</text>
  
  <!-- 场景2：忘记吃药 -->
  <rect x="720" y="280" width="550" height="200" rx="20" fill="white" stroke="#10b981" stroke-width="2"/>
  <text x="740" y="320" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">用药复杂易忘记</text>
  
  <!-- 忘记吃药场景 -->
  <g transform="translate(800,350)">
    <circle cx="50" cy="30" r="25" fill="#f3f4f6"/>
    <!-- 老人头像 -->
    <circle cx="50" cy="20" r="12" fill="#6b7280"/>
    <ellipse cx="50" cy="40" rx="15" ry="10" fill="#6b7280"/>
    <!-- 困惑表情 -->
    <circle cx="45" cy="17" r="2" fill="white"/>
    <circle cx="55" cy="17" r="2" fill="white"/>
    <path d="M45,25 Q50,30 55,25" stroke="white" stroke-width="2" fill="none"/>
    
    <!-- 药盒 -->
    <rect x="100" y="15" width="40" height="30" rx="3" fill="#ef4444"/>
    <rect x="105" y="20" width="8" height="8" rx="2" fill="white"/>
    <rect x="117" y="20" width="8" height="8" rx="2" fill="white"/>
    <rect x="129" y="20" width="8" height="8" rx="2" fill="white"/>
    <rect x="105" y="32" width="8" height="8" rx="2" fill="white"/>
    <rect x="117" y="32" width="8" height="8" rx="2" fill="white"/>
    <rect x="129" y="32" width="8" height="8" rx="2" fill="white"/>
    
    <!-- 问号 -->
    <text x="75" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#ef4444">?</text>
  </g>
  
  <text x="740" y="420" font-family="Microsoft YaHei" font-size="24" fill="#374151">多种药物，服用时间复杂</text>
  <text x="740" y="450" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">经常忘记或吃错药</text>
  
  <!-- 场景3：就医咨询不便 -->
  <rect x="1320" y="280" width="480" height="200" rx="20" fill="white" stroke="#10b981" stroke-width="2"/>
  <text x="1340" y="320" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">就医咨询不方便</text>
  
  <!-- 对话场景 -->
  <g transform="translate(1400,350)">
    <!-- 子女 -->
    <circle cx="30" cy="20" r="15" fill="#3b82f6"/>
    <ellipse cx="30" cy="35" rx="10" ry="8" fill="#3b82f6"/>
    
    <!-- 对话气泡 -->
    <rect x="60" y="5" width="120" height="30" rx="15" fill="#f3f4f6"/>
    <path d="M60,20 L50,25 L60,30" fill="#f3f4f6"/>
    <text x="120" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#374151">爸，血压怎么样？</text>
    
    <!-- 老人 -->
    <circle cx="250" cy="20" r="15" fill="#6b7280"/>
    <ellipse cx="250" cy="35" rx="10" ry="8" fill="#6b7280"/>
    
    <!-- 回答气泡 -->
    <rect x="120" y="40" width="100" height="25" rx="12" fill="#fef3c7"/>
    <path d="M220,50 L230,55 L220,60" fill="#fef3c7"/>
    <text x="170" y="57" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#374151">还行吧...</text>
  </g>
  
  <text x="1340" y="420" font-family="Microsoft YaHei" font-size="24" fill="#374151">健康状况说不清楚</text>
  <text x="1340" y="450" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">子女难以掌握实际情况</text>
  
  <!-- 痛点总结 -->
  <rect x="120" y="520" width="1680" height="100" rx="15" fill="#10b981" opacity="0.1"/>
  <text x="160" y="560" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">核心痛点</text>
  <text x="160" y="595" font-family="Microsoft YaHei" font-size="28" fill="#374151">慢病数据难管理？ | 就医咨询不方便？ | 用药复杂易忘记？ | 子女难掌握健康状况？</text>
  
  <!-- 解决方案引出 -->
  <rect x="120" y="660" width="1680" height="200" rx="25" fill="#10b981"/>
  <text x="960" y="720" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="white">我们的健康监测设备和服务</text>
  <text x="960" y="780" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" fill="white">让健康管理变得更简单、更智能、更有效！</text>
  
  <!-- 解决方案预览 -->
  <g transform="translate(960,820)">
    <rect x="-240" y="0" width="150" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="-165" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#10b981">智能监测设备</text>
    
    <rect x="-60" y="0" width="120" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#10b981">数据自动上传</text>
    
    <rect x="90" y="0" width="150" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="165" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#10b981">智能用药提醒</text>
  </g>
  
  <!-- 价值主张 -->
  <rect x="120" y="920" width="1680" height="80" rx="15" fill="#1e3a8a"/>
  <text x="960" y="970" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">健康管理从被动变主动，从复杂变简单！</text>
  
  <!-- 装饰元素 -->
  <circle cx="80" cy="600" r="20" fill="#10b981" opacity="0.2"/>
  <circle cx="1840" cy="600" r="20" fill="#10b981" opacity="0.2"/>
  
  <!-- 健康图标装饰 -->
  <g transform="translate(1750,350)">
    <circle cx="0" cy="0" r="30" fill="#10b981" opacity="0.1"/>
    <path d="M-10,-10 Q0,-20 10,-10 Q0,0 -10,-10 M0,0 L0,15 M-8,7 L8,7" stroke="#10b981" stroke-width="3" fill="none"/>
  </g>
</svg>
