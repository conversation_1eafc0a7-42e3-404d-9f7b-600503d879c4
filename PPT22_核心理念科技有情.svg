<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fef3c7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#92400e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:0.6" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#fde68a" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">我们的灵魂：科技有"智"，更有"情"</text>
  
  <!-- 核心理念 -->
  <rect x="120" y="200" width="1680" height="100" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <text x="960" y="240" text-anchor="middle" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#92400e">核心理念</text>
  <text x="960" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" fill="#374151">科技赋能，守护银龄生活</text>
  
  <!-- 四大价值支柱 -->
  <text x="960" y="360" text-anchor="middle" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#92400e">四大价值支柱</text>
  
  <!-- Safe (安全) -->
  <rect x="120" y="400" width="400" height="200" rx="20" fill="white" stroke="#ef4444" stroke-width="3"/>
  <circle cx="220" cy="470" r="50" fill="#ef4444"/>
  <path d="M200,455 L210,465 L240,435" stroke="white" stroke-width="6" fill="none"/>
  <text x="320" y="450" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#dc2626">Safe</text>
  <text x="320" y="485" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#374151">安全</text>
  <text x="140" y="530" font-family="Microsoft YaHei" font-size="24" fill="#374151">守护生命线</text>
  <text x="140" y="560" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">让家更安心</text>
  <text x="140" y="585" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">紧急救援·环境监测·活动守护</text>
  
  <!-- Healthy (健康) -->
  <rect x="560" y="400" width="400" height="200" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="660" cy="470" r="50" fill="#10b981"/>
  <path d="M645,455 Q660,440 675,455 Q660,485 645,455 M660,485 L660,495" stroke="white" stroke-width="5" fill="none"/>
  <text x="760" y="450" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#065f46">Healthy</text>
  <text x="760" y="485" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#374151">健康</text>
  <text x="580" y="530" font-family="Microsoft YaHei" font-size="24" fill="#374151">科学管理</text>
  <text x="580" y="560" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">乐享活力</text>
  <text x="580" y="585" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">健康监测·慢病管理·用药提醒</text>
  
  <!-- Convenient (便捷) -->
  <rect x="1000" y="400" width="400" height="200" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="1100" cy="470" r="50" fill="#3b82f6"/>
  <rect x="1085" y="455" width="30" height="30" rx="5" fill="white"/>
  <circle cx="1100" cy="470" r="8" fill="#3b82f6"/>
  <text x="1200" y="450" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">Convenient</text>
  <text x="1200" y="485" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#374151">便捷</text>
  <text x="1020" y="530" font-family="Microsoft YaHei" font-size="24" fill="#374151">智享生活</text>
  <text x="1020" y="560" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">轻松自在</text>
  <text x="1020" y="585" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">语音控制·智能家居·便民服务</text>
  
  <!-- Caring (关爱) -->
  <rect x="1440" y="400" width="360" height="200" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="1540" cy="470" r="50" fill="#f59e0b"/>
  <path d="M1525,460 Q1535,450 1545,460 Q1535,480 1525,460 M1545,460 Q1555,450 1565,460 Q1555,480 1545,460" fill="white"/>
  <text x="1620" y="450" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#92400e">Caring</text>
  <text x="1620" y="485" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#374151">关爱</text>
  <text x="1460" y="530" font-family="Microsoft YaHei" font-size="24" fill="#374151">情牵一线</text>
  <text x="1460" y="560" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">温暖常在</text>
  <text x="1460" y="585" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">亲情互动·内容娱乐</text>
  
  <!-- 温暖的心 -->
  <g transform="translate(960,750)">
    <path d="M-40,0 Q-60,-20 -80,0 Q-60,40 0,80 Q60,40 80,0 Q60,-20 40,0 Q20,-20 0,0 Q-20,-20 -40,0" fill="url(#heartGradient)"/>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">科技</text>
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">有温度</text>
  </g>
  
  <!-- 讲解要点 -->
  <rect x="120" y="850" width="1680" height="120" rx="20" fill="#92400e" opacity="0.1"/>
  <text x="160" y="890" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">讲解要点</text>
  <text x="160" y="925" font-family="Microsoft YaHei" font-size="28" fill="#374151">请再次记住这四个词！这是我们所有产品和服务的出发点和落脚点，</text>
  <text x="160" y="955" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">也是我们向客户传递价值的核心信息。</text>
  
  <!-- 价值强调 -->
  <rect x="120" y="990" width="1680" height="80" rx="15" fill="#dc2626"/>
  <text x="960" y="1020" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">我们提供的不仅仅是技术，</text>
  <text x="960" y="1050" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">更是这份安全感、健康管理、生活便利和情感关怀！</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(100,500)">
    <circle cx="0" cy="0" r="30" fill="#ef4444" opacity="0.2"/>
    <path d="M-15,0 L0,-15 L15,0 L0,15 Z" fill="#ef4444" opacity="0.4"/>
  </g>
  
  <g transform="translate(1820,500)">
    <circle cx="0" cy="0" r="30" fill="#f59e0b" opacity="0.2"/>
    <path d="M-10,-10 Q0,-20 10,-10 Q0,0 -10,-10 M0,0 L0,15" stroke="#f59e0b" stroke-width="3" fill="none"/>
  </g>
  
  <!-- 连接线装饰 -->
  <path d="M320,600 Q480,650 640,600" stroke="#fde68a" stroke-width="2" stroke-dasharray="5,5" fill="none"/>
  <path d="M760,600 Q920,650 1080,600" stroke="#fde68a" stroke-width="2" stroke-dasharray="5,5" fill="none"/>
  <path d="M1200,600 Q1320,650 1440,600" stroke="#fde68a" stroke-width="2" stroke-dasharray="5,5" fill="none"/>
</svg>
