<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#bfdbfe" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">核心需求聚焦三：便捷 - 科技让生活更简单</text>
  
  <!-- 痛点场景展示 -->
  <text x="120" y="240" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#1e3a8a">典型痛点场景</text>
  
  <!-- 场景1：操作复杂 -->
  <rect x="120" y="280" width="800" height="250" rx="20" fill="white" stroke="#3b82f6" stroke-width="2"/>
  <text x="140" y="320" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">智能设备操作难</text>
  
  <!-- 复杂遥控器场景 -->
  <g transform="translate(200,350)">
    <!-- 老人困惑表情 -->
    <circle cx="80" cy="50" r="40" fill="#f3f4f6"/>
    <circle cx="80" cy="35" r="20" fill="#6b7280"/>
    <ellipse cx="80" cy="65" rx="25" ry="15" fill="#6b7280"/>
    <!-- 困惑表情 -->
    <circle cx="72" cy="30" r="3" fill="white"/>
    <circle cx="88" cy="30" r="3" fill="white"/>
    <path d="M72,42 Q80,50 88,42" stroke="white" stroke-width="2" fill="none"/>
    <path d="M65,20 Q70,15 75,20 M85,20 Q90,15 95,20" stroke="#6b7280" stroke-width="2" fill="none"/>
    
    <!-- 复杂遥控器 -->
    <rect x="200" y="20" width="60" height="120" rx="8" fill="#374151"/>
    <rect x="210" y="30" width="40" height="20" rx="3" fill="#1f2937"/>
    <!-- 密密麻麻的按钮 -->
    <circle cx="220" cy="60" r="4" fill="#ef4444"/>
    <circle cx="235" cy="60" r="4" fill="#10b981"/>
    <circle cx="250" cy="60" r="4" fill="#f59e0b"/>
    <rect x="215" y="70" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="227" y="70" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="239" y="70" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="215" y="80" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="227" y="80" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="239" y="80" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="215" y="90" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="227" y="90" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="239" y="90" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="215" y="100" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="227" y="100" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="239" y="100" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="215" y="110" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="227" y="110" width="8" height="6" rx="1" fill="#6b7280"/>
    <rect x="239" y="110" width="8" height="6" rx="1" fill="#6b7280"/>
    
    <!-- 困惑气泡 -->
    <ellipse cx="150" cy="30" rx="40" ry="20" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
    <path d="M130,40 L120,50 L140,45" fill="#fef3c7"/>
    <text x="150" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#92400e">这么多按钮</text>
    <text x="150" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#92400e">哪个是哪个？</text>
    
    <!-- 复杂APP界面 -->
    <rect x="320" y="30" width="80" height="120" rx="8" fill="#1f2937"/>
    <rect x="325" y="35" width="70" height="15" rx="2" fill="#3b82f6"/>
    <text x="360" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="8" fill="white">智能家居</text>
    <!-- 复杂界面元素 -->
    <rect x="330" y="55" width="15" height="15" rx="2" fill="#ef4444"/>
    <rect x="350" y="55" width="15" height="15" rx="2" fill="#10b981"/>
    <rect x="370" y="55" width="15" height="15" rx="2" fill="#f59e0b"/>
    <rect x="330" y="75" width="15" height="15" rx="2" fill="#8b5cf6"/>
    <rect x="350" y="75" width="15" height="15" rx="2" fill="#06b6d4"/>
    <rect x="370" y="75" width="15" height="15" rx="2" fill="#84cc16"/>
    <rect x="330" y="95" width="55" height="8" rx="1" fill="#6b7280"/>
    <rect x="330" y="108" width="55" height="8" rx="1" fill="#6b7280"/>
    <rect x="330" y="121" width="55" height="8" rx="1" fill="#6b7280"/>
    <rect x="330" y="134" width="55" height="8" rx="1" fill="#6b7280"/>
  </g>
  
  <text x="140" y="450" font-family="Microsoft YaHei" font-size="24" fill="#374151">遥控器按钮太多，APP界面复杂</text>
  <text x="140" y="480" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">老人学不会，用不好</text>
  <text x="140" y="510" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">科技本应简化生活，却成了负担</text>
  
  <!-- 场景2：生活琐事繁琐 -->
  <rect x="1000" y="280" width="700" height="250" rx="20" fill="white" stroke="#3b82f6" stroke-width="2"/>
  <text x="1020" y="320" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">生活琐事太繁琐</text>
  
  <!-- 买菜缴费场景 -->
  <g transform="translate(1100,350)">
    <!-- 老人提重物 -->
    <circle cx="50" cy="40" r="25" fill="#f3f4f6"/>
    <circle cx="50" cy="30" r="15" fill="#6b7280"/>
    <ellipse cx="50" cy="50" rx="18" ry="12" fill="#6b7280"/>
    <!-- 疲惫表情 -->
    <circle cx="45" cy="27" r="2" fill="white"/>
    <circle cx="55" cy="27" r="2" fill="white"/>
    <path d="M42,35 Q50,40 58,35" stroke="white" stroke-width="2" fill="none"/>
    
    <!-- 重物袋子 -->
    <ellipse cx="20" cy="65" rx="12" ry="8" fill="#10b981"/>
    <path d="M15,57 Q20,52 25,57" stroke="#065f46" stroke-width="2" fill="none"/>
    <ellipse cx="80" cy="65" rx="12" ry="8" fill="#f59e0b"/>
    <path d="M75,57 Q80,52 85,57" stroke="#92400e" stroke-width="2" fill="none"/>
    
    <!-- 银行/缴费场景 -->
    <rect x="150" y="20" width="80" height="60" rx="5" fill="#f3f4f6" stroke="#6b7280" stroke-width="2"/>
    <text x="190" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#374151">银行</text>
    <text x="190" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#6b7280">排队缴费</text>
    <text x="190" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#6b7280">手续复杂</text>
    
    <!-- 排队人群 -->
    <circle cx="270" cy="35" r="8" fill="#3b82f6"/>
    <circle cx="290" cy="35" r="8" fill="#10b981"/>
    <circle cx="310" cy="35" r="8" fill="#f59e0b"/>
    <text x="290" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="#6b7280">排长队</text>
    
    <!-- 疲惫气泡 -->
    <ellipse cx="120" cy="20" rx="35" ry="15" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
    <path d="M100,30 L90,40 L110,35" fill="#fef2f2"/>
    <text x="120" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#dc2626">太累了...</text>
  </g>
  
  <text x="1020" y="450" font-family="Microsoft YaHei" font-size="24" fill="#374151">买菜购物、缴费办事需要跑腿</text>
  <text x="1020" y="480" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">体力不支，出行不便</text>
  <text x="1020" y="510" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">信息获取不及时，服务不便民</text>
  
  <!-- 痛点总结 -->
  <rect x="120" y="560" width="1680" height="100" rx="15" fill="#3b82f6" opacity="0.1"/>
  <text x="160" y="600" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">核心痛点</text>
  <text x="160" y="635" font-family="Microsoft YaHei" font-size="28" fill="#374151">智能设备操作难？ | 生活琐事太繁琐？ | 信息获取不及时？</text>
  
  <!-- 解决方案引出 -->
  <rect x="120" y="700" width="1680" height="200" rx="25" fill="#3b82f6"/>
  <text x="960" y="760" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="white">我们的智能音箱、线上服务平台</text>
  <text x="960" y="820" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" fill="white">用科技简化生活，提升品质</text>
  
  <!-- 解决方案预览 -->
  <g transform="translate(960,860)">
    <rect x="-200" y="0" width="120" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="-140" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#3b82f6">语音控制</text>
    
    <rect x="-60" y="0" width="120" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#3b82f6">线上服务</text>
    
    <rect x="80" y="0" width="120" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="140" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#3b82f6">智能家居</text>
  </g>
  
  <!-- 价值主张 -->
  <rect x="120" y="960" width="1680" height="80" rx="15" fill="#1e3a8a"/>
  <text x="960" y="1010" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">动动嘴，都搞定！让科技真正为老人服务！</text>
  
  <!-- 装饰元素 -->
  <circle cx="80" cy="640" r="20" fill="#3b82f6" opacity="0.2"/>
  <circle cx="1840" cy="640" r="20" fill="#3b82f6" opacity="0.2"/>
</svg>
