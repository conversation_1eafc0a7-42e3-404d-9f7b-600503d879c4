<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#e5e7eb" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">第一站小结：看清局，明方向</text>
  
  <!-- 小结内容 -->
  <text x="960" y="220" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#6b7280">模块一小结 &amp; Q&amp;A</text>
  
  <!-- 五个关键词回顾 -->
  <g transform="translate(960,450)">
    <!-- 中心圆 -->
    <circle cx="0" cy="0" r="100" fill="#1e3a8a" opacity="0.1" stroke="#1e3a8a" stroke-width="3"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">智慧康养</text>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">市场洞察</text>
    
    <!-- 市场：蓝海广阔 -->
    <g transform="translate(-300,-150)">
      <rect x="-80" y="-40" width="160" height="80" rx="20" fill="#0ea5e9"/>
      <circle cx="0" cy="-60" r="25" fill="#0ea5e9" opacity="0.3"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">市场</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">蓝海广阔</text>
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#374151">万亿级规模</text>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#374151">刚性需求</text>
    </g>
    
    <!-- 需求：四维升级 -->
    <g transform="translate(300,-150)">
      <rect x="-80" y="-40" width="160" height="80" rx="20" fill="#10b981"/>
      <circle cx="0" cy="-60" r="25" fill="#10b981" opacity="0.3"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">需求</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">四维升级</text>
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">安全·健康</text>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">便捷·关爱</text>
    </g>
    
    <!-- 政策：东风正劲 -->
    <g transform="translate(-300,150)">
      <rect x="-80" y="-40" width="160" height="80" rx="20" fill="#f59e0b"/>
      <circle cx="0" cy="-60" r="25" fill="#f59e0b" opacity="0.3"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">政策</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">东风正劲</text>
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#374151">国家战略</text>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#374151">地方支持</text>
    </g>
    
    <!-- 痛点：机会所在 -->
    <g transform="translate(300,150)">
      <rect x="-80" y="-40" width="160" height="80" rx="20" fill="#8b5cf6"/>
      <circle cx="0" cy="-60" r="25" fill="#8b5cf6" opacity="0.3"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">痛点</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">机会所在</text>
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#374151">用户抱怨</text>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#374151">行业困难</text>
    </g>
    
    <!-- 电信：优势在我，定位明确 -->
    <g transform="translate(0,250)">
      <rect x="-120" y="-40" width="240" height="80" rx="20" fill="#dc2626"/>
      <circle cx="0" cy="-60" r="25" fill="#dc2626" opacity="0.3"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">电信</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">优势在我，定位明确</text>
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#374151">六大优势 | 战略清晰</text>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#374151">技术+服务双轮驱动</text>
    </g>
    
    <!-- 连接线 -->
    <path d="M-80,-50 L-220,-100" stroke="#6b7280" stroke-width="2" stroke-dasharray="5,5"/>
    <path d="M80,-50 L220,-100" stroke="#6b7280" stroke-width="2" stroke-dasharray="5,5"/>
    <path d="M-80,50 L-220,100" stroke="#6b7280" stroke-width="2" stroke-dasharray="5,5"/>
    <path d="M80,50 L220,100" stroke="#6b7280" stroke-width="2" stroke-dasharray="5,5"/>
    <path d="M0,100 L0,200" stroke="#6b7280" stroke-width="2" stroke-dasharray="5,5"/>
  </g>
  
  <!-- 互动环节 -->
  <rect x="120" y="750" width="1680" height="200" rx="25" fill="#f0f9ff" stroke="#3b82f6" stroke-width="3"/>
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">互动时间</text>
  <text x="960" y="850" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#374151">关于市场和我们的定位，大家还有什么疑问吗？</text>
  
  <!-- 时间提示 -->
  <rect x="400" y="880" width="1120" height="50" rx="15" fill="#3b82f6" opacity="0.1"/>
  <text x="960" y="910" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">预留5分钟左右进行Q&A互动</text>
  
  <!-- 过渡提示 -->
  <rect x="120" y="980" width="1680" height="80" rx="15" fill="#10b981"/>
  <text x="960" y="1010" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">第一站完成！接下来我们进入第二站：</text>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">利器在握 - 电信智慧康养产品体系详解</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(100,300)">
    <circle cx="0" cy="0" r="30" fill="#3b82f6" opacity="0.1"/>
    <path d="M-15,0 L0,-15 L15,0 L0,15 Z" fill="#3b82f6" opacity="0.3"/>
  </g>
  
  <g transform="translate(1820,300)">
    <circle cx="0" cy="0" r="30" fill="#10b981" opacity="0.1"/>
    <path d="M-10,-10 L10,10 M10,-10 L-10,10" stroke="#10b981" stroke-width="3"/>
    <circle cx="0" cy="0" r="8" fill="#10b981"/>
  </g>
  
  <!-- 问号装饰 -->
  <g transform="translate(200,850)">
    <circle cx="0" cy="0" r="25" fill="#f59e0b" opacity="0.2"/>
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#f59e0b">?</text>
  </g>
  
  <g transform="translate(1720,850)">
    <circle cx="0" cy="0" r="25" fill="#f59e0b" opacity="0.2"/>
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#f59e0b">?</text>
  </g>
</svg>
