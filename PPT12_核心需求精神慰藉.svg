<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fef3c7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#92400e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#fde68a" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">核心需求聚焦四：精神 - 驱散孤独，传递关爱</text>
  
  <!-- 痛点场景展示 -->
  <text x="120" y="240" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#92400e">典型痛点场景</text>
  
  <!-- 场景1：独居孤独 -->
  <rect x="120" y="280" width="800" height="280" rx="20" fill="white" stroke="#f59e0b" stroke-width="2"/>
  <text x="140" y="320" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#92400e">情感孤独缺陪伴</text>
  
  <!-- 独居老人场景 -->
  <g transform="translate(300,380)">
    <!-- 房间轮廓 -->
    <rect x="0" y="0" width="200" height="120" rx="5" fill="#f8fafc" stroke="#e5e7eb" stroke-width="2"/>
    
    <!-- 窗户 -->
    <rect x="20" y="20" width="40" height="30" rx="3" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1"/>
    <path d="M20,35 L60,35 M40,20 L40,50" stroke="#3b82f6" stroke-width="1"/>
    
    <!-- 孤独的老人背影 -->
    <g transform="translate(120,60)">
      <circle cx="0" cy="-20" r="12" fill="#6b7280"/>
      <ellipse cx="0" cy="0" rx="15" ry="20" fill="#6b7280"/>
      <rect x="-8" y="20" width="6" height="15" fill="#6b7280"/>
      <rect x="2" y="20" width="6" height="15" fill="#6b7280"/>
      <!-- 弯曲的背 -->
      <path d="M-15,-10 Q-20,0 -15,10" stroke="#6b7280" stroke-width="3" fill="none"/>
    </g>
    
    <!-- 空椅子 -->
    <rect x="80" y="80" width="25" height="30" rx="2" fill="#d1d5db"/>
    <rect x="75" y="75" width="35" height="5" rx="2" fill="#d1d5db"/>
    
    <!-- 电视（关着） -->
    <rect x="150" y="70" width="35" height="25" rx="3" fill="#374151"/>
    <rect x="155" y="75" width="25" height="15" rx="2" fill="#1f2937"/>
    
    <!-- 孤独气氛 -->
    <text x="100" y="140" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#6b7280">静悄悄...</text>
  </g>
  
  <text x="140" y="480" font-family="Microsoft YaHei" font-size="24" fill="#374151">独居老人缺乏陪伴，内心孤独</text>
  <text x="140" y="510" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">精神空虚，缺乏生活乐趣</text>
  <text x="140" y="540" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">特别是节假日，孤独感更强烈</text>
  
  <!-- 场景2：沟通障碍 -->
  <rect x="1000" y="280" width="700" height="280" rx="20" fill="white" stroke="#f59e0b" stroke-width="2"/>
  <text x="1020" y="320" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#92400e">与子女沟通有障碍</text>
  
  <!-- 视频通话困难场景 -->
  <g transform="translate(1150,380)">
    <!-- 老人面对手机/平板困惑 -->
    <circle cx="80" cy="50" r="30" fill="#f3f4f6"/>
    <circle cx="80" cy="40" r="18" fill="#6b7280"/>
    <ellipse cx="80" cy="60" rx="22" ry="12" fill="#6b7280"/>
    <!-- 困惑表情 -->
    <circle cx="72" cy="35" r="3" fill="white"/>
    <circle cx="88" cy="35" r="3" fill="white"/>
    <path d="M72,48 Q80,55 88,48" stroke="white" stroke-width="2" fill="none"/>
    <path d="M65,25 Q70,20 75,25 M85,25 Q90,20 95,25" stroke="#6b7280" stroke-width="2" fill="none"/>
    
    <!-- 平板/手机 -->
    <rect x="150" y="30" width="60" height="40" rx="5" fill="#1f2937"/>
    <rect x="155" y="35" width="50" height="30" rx="3" fill="#3b82f6"/>
    <text x="180" y="52" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="white">视频通话</text>
    
    <!-- 困惑气泡 -->
    <ellipse cx="120" cy="20" rx="35" ry="15" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
    <path d="M105,30 L95,40 L115,35" fill="#fef2f2"/>
    <text x="120" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#dc2626">怎么用啊？</text>
    
    <!-- 子女在屏幕里着急 -->
    <g transform="translate(155,35)">
      <rect x="0" y="0" width="50" height="30" rx="3" fill="#3b82f6"/>
      <circle cx="25" cy="15" r="8" fill="white"/>
      <circle cx="25" cy="12" r="4" fill="#3b82f6"/>
      <ellipse cx="25" cy="20" rx="6" ry="4" fill="#3b82f6"/>
      <!-- 着急表情 -->
      <circle cx="22" cy="10" r="1" fill="white"/>
      <circle cx="28" cy="10" r="1" fill="white"/>
      <path d="M20,15 Q25,18 30,15" stroke="white" stroke-width="1" fill="none"/>
    </g>
    
    <!-- 距离感 -->
    <path d="M50,70 Q100,90 150,70" stroke="#6b7280" stroke-width="2" stroke-dasharray="3,3" fill="none"/>
    <text x="100" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="#6b7280">距离感</text>
  </g>
  
  <text x="1020" y="480" font-family="Microsoft YaHei" font-size="24" fill="#374151">想和子女视频却不会操作</text>
  <text x="1020" y="510" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">沟通不畅，情感疏离</text>
  <text x="1020" y="540" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">缺乏社交与娱乐活动</text>
  
  <!-- 痛点总结 -->
  <rect x="120" y="590" width="1680" height="100" rx="15" fill="#f59e0b" opacity="0.1"/>
  <text x="160" y="630" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">核心痛点</text>
  <text x="160" y="665" font-family="Microsoft YaHei" font-size="28" fill="#374151">情感孤独缺陪伴？ | 与子女沟通有障碍？ | 缺乏社交与娱乐？</text>
  
  <!-- 解决方案引出 -->
  <rect x="120" y="730" width="1680" height="200" rx="25" fill="#f59e0b"/>
  <text x="960" y="790" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="white">技术也可以有温度！</text>
  <text x="960" y="850" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" fill="white">我们的亲情互动、内容服务，满足老人的精神需求</text>
  
  <!-- 解决方案预览 -->
  <g transform="translate(960,890)">
    <rect x="-240" y="0" width="150" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="-165" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#f59e0b">简易视频通话</text>
    
    <rect x="-60" y="0" width="120" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#f59e0b">内容娱乐</text>
    
    <rect x="90" y="0" width="150" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="165" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#f59e0b">社交互动</text>
  </g>
  
  <!-- 价值主张 -->
  <rect x="120" y="990" width="1680" height="80" rx="15" fill="#1e3a8a"/>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">用科技传递温暖，让老人不再孤单！</text>
  
  <!-- 装饰元素 -->
  <circle cx="80" cy="670" r="20" fill="#f59e0b" opacity="0.2"/>
  <circle cx="1840" cy="670" r="20" fill="#f59e0b" opacity="0.2"/>
  
  <!-- 爱心装饰 -->
  <g transform="translate(1750,400)">
    <path d="M0,10 Q-10,0 -20,10 Q-10,20 0,30 Q10,20 20,10 Q10,0 0,10" fill="#f59e0b" opacity="0.3"/>
  </g>
  
  <g transform="translate(150,400)">
    <path d="M0,10 Q-10,0 -20,10 Q-10,20 0,30 Q10,20 20,10 Q10,0 0,10" fill="#f59e0b" opacity="0.3"/>
  </g>
</svg>
