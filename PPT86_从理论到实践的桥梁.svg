<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bridgeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#bfdbfe" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">跨越鸿沟：从"听懂"到"会用"</text>
  
  <!-- 桥梁图 -->
  <g transform="translate(960,500)">
    <!-- 左岸 - Day 1 理论知识 -->
    <g transform="translate(-600,0)">
      <!-- 岸边 -->
      <rect x="-150" y="-100" width="300" height="200" rx="20" fill="#10b981"/>
      <rect x="-140" y="-90" width="280" height="180" rx="15" fill="white"/>
      
      <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">左岸</text>
      <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#065f46">Day 1 理论知识</text>
      
      <!-- 知识要素 -->
      <circle cx="-80" cy="20" r="15" fill="#10b981"/>
      <text x="-80" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="white">市场</text>
      
      <circle cx="-30" cy="20" r="15" fill="#3b82f6"/>
      <text x="-30" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="white">产品</text>
      
      <circle cx="20" cy="20" r="15" fill="#f59e0b"/>
      <text x="20" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="white">客户</text>
      
      <circle cx="70" cy="20" r="15" fill="#ef4444"/>
      <text x="70" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="white">SPIN</text>
      
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">方法工具</text>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">理论基础</text>
      
      <!-- 左岸装饰 -->
      <circle cx="-120" cy="-120" r="20" fill="#10b981" opacity="0.3"/>
      <circle cx="120" cy="-120" r="15" fill="#10b981" opacity="0.2"/>
    </g>
    
    <!-- 桥梁 - Day 2-4 实战拉练 -->
    <g>
      <!-- 桥身 -->
      <rect x="-300" y="-30" width="600" height="60" rx="30" fill="url(#bridgeGradient)"/>
      <rect x="-290" y="-20" width="580" height="40" rx="20" fill="white" opacity="0.9"/>
      
      <!-- 桥墩 -->
      <rect x="-200" y="30" width="40" height="80" rx="5" fill="#6b7280"/>
      <rect x="-50" y="30" width="40" height="80" rx="5" fill="#6b7280"/>
      <rect x="100" y="30" width="40" height="80" rx="5" fill="#6b7280"/>
      
      <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">桥梁</text>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">Day 2-4 实战拉练</text>
      
      <!-- 桥梁要素 -->
      <text x="-180" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">现场辅导</text>
      <text x="-30" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">复盘反思</text>
      <text x="120" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">持续改进</text>
      
      <!-- 桥梁装饰 -->
      <circle cx="-250" cy="-60" r="8" fill="#3b82f6" opacity="0.5"/>
      <circle cx="0" cy="-60" r="10" fill="#f59e0b" opacity="0.5"/>
      <circle cx="250" cy="-60" r="8" fill="#ef4444" opacity="0.5"/>
    </g>
    
    <!-- 右岸 - Day 4+ 实战能力提升 -->
    <g transform="translate(600,0)">
      <!-- 岸边 -->
      <rect x="-150" y="-100" width="300" height="200" rx="20" fill="#ef4444"/>
      <rect x="-140" y="-90" width="280" height="180" rx="15" fill="white"/>
      
      <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#dc2626">右岸</text>
      <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#dc2626">Day 4+ 实战能力提升</text>
      
      <!-- 能力要素 -->
      <circle cx="-80" cy="20" r="15" fill="#10b981"/>
      <text x="-80" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="white">开口</text>
      
      <circle cx="-30" cy="20" r="15" fill="#3b82f6"/>
      <text x="-30" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="white">呈现</text>
      
      <circle cx="20" cy="20" r="15" fill="#f59e0b"/>
      <text x="20" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="white">异议</text>
      
      <circle cx="70" cy="20" r="15" fill="#8b5cf6"/>
      <text x="70" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="white">推进</text>
      
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">业绩增长</text>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">实战能力</text>
      
      <!-- 右岸装饰 -->
      <circle cx="-120" cy="-120" r="20" fill="#ef4444" opacity="0.3"/>
      <circle cx="120" cy="-120" r="15" fill="#ef4444" opacity="0.2"/>
    </g>
    
    <!-- 水流 -->
    <path d="M-600,150 Q-300,180 0,150 Q300,120 600,150" stroke="#3b82f6" stroke-width="3" fill="none" opacity="0.5" stroke-dasharray="10,5"/>
    <text x="0" y="180" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#3b82f6" opacity="0.7">知识转化之河</text>
  </g>
  
  <!-- 转化要素 -->
  <rect x="120" y="750" width="1680" height="200" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <text x="160" y="790" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">转化关键要素</text>
  
  <!-- 实践 -->
  <g transform="translate(300,850)">
    <circle cx="0" cy="0" r="40" fill="#10b981"/>
    <circle cx="0" cy="0" r="25" fill="white"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#10b981">实践</text>
    <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#065f46">实践</text>
    <text x="0" y="95" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">真实场景</text>
  </g>
  
  <!-- 反思 -->
  <g transform="translate(550,850)">
    <circle cx="0" cy="0" r="40" fill="#3b82f6"/>
    <circle cx="0" cy="0" r="25" fill="white"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#3b82f6">反思</text>
    <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1e3a8a">反思</text>
    <text x="0" y="95" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">深度复盘</text>
  </g>
  
  <!-- 改进 -->
  <g transform="translate(800,850)">
    <circle cx="0" cy="0" r="40" fill="#f59e0b"/>
    <circle cx="0" cy="0" r="25" fill="white"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#f59e0b">改进</text>
    <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#92400e">改进</text>
    <text x="0" y="95" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">持续优化</text>
  </g>
  
  <!-- 提升 -->
  <g transform="translate(1050,850)">
    <circle cx="0" cy="0" r="40" fill="#ef4444"/>
    <circle cx="0" cy="0" r="25" fill="white"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#ef4444">提升</text>
    <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#dc2626">提升</text>
    <text x="0" y="95" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">能力跃升</text>
  </g>
  
  <!-- 成功 -->
  <g transform="translate(1300,850)">
    <circle cx="0" cy="0" r="40" fill="#8b5cf6"/>
    <circle cx="0" cy="0" r="25" fill="white"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#8b5cf6">成功</text>
    <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#6b21a8">成功</text>
    <text x="0" y="95" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">业绩突破</text>
  </g>
  
  <!-- 连接箭头 -->
  <path d="M350,850 L500,850" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  <path d="M600,850 L750,850" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  <path d="M850,850 L1000,850" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  <path d="M1100,850 L1250,850" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280"/>
    </marker>
  </defs>
  
  <!-- 讲解要点 -->
  <rect x="120" y="970" width="1680" height="100" rx="20" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="1005" font-family="Microsoft YaHei" font-size="28" fill="#374151">"真正的学习发生在实践中！未来三天，就是我们共同搭建这座桥梁，</text>
  <text x="160" y="1035" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">把理论转化为实实在在的能力！"</text>
  <text x="160" y="1060" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">From Knowledge to Capability, From Theory to Practice</text>
</svg>
