<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="weaponGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#3b82f6" stroke-width="4" fill="none" opacity="0.6"/>
  <path d="M0,200 Q480,150 960,200 T1920,200" stroke="#1e3a8a" stroke-width="3" fill="none" opacity="0.4"/>
  
  <!-- 模块标识 -->
  <rect x="120" y="120" width="200" height="80" rx="15" fill="#dc2626"/>
  <text x="220" y="170" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">模块二</text>
  
  <!-- 主标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)">利器在握</text>
  <text x="960" y="260" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#374151">解密电信智慧康养"武器库"</text>
  
  <!-- 模块目标 -->
  <rect x="120" y="320" width="1680" height="120" rx="20" fill="url(#weaponGradient)" stroke="#dc2626" stroke-width="2"/>
  <text x="160" y="360" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#dc2626">模块目标</text>
  <text x="160" y="400" font-family="Microsoft YaHei" font-size="32" fill="#374151">知己知彼，百战不殆。这一章，我们来深入了解我们手中的"武器"</text>
  <text x="160" y="430" font-family="Microsoft YaHei" font-size="32" fill="#374151" font-weight="bold">——电信的智慧康养产品和服务，以及它们的核心价值。</text>
  
  <!-- 核心议题 -->
  <text x="120" y="520" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#dc2626">核心议题</text>
  
  <!-- 议题卡片 -->
  <rect x="120" y="560" width="400" height="140" rx="15" fill="white" stroke="#dc2626" stroke-width="2"/>
  <circle cx="180" cy="610" r="25" fill="#dc2626"/>
  <text x="180" y="620" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">理念</text>
  <text x="230" y="595" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#dc2626">核心理念</text>
  <text x="140" y="630" font-family="Microsoft YaHei" font-size="22" fill="#374151">科技有"智"</text>
  <text x="140" y="655" font-family="Microsoft YaHei" font-size="22" fill="#374151">更有"情"</text>
  <text x="140" y="680" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">四大价值支柱</text>
  
  <rect x="560" y="560" width="400" height="140" rx="15" fill="white" stroke="#10b981" stroke-width="2"/>
  <circle cx="620" cy="610" r="25" fill="#10b981"/>
  <text x="620" y="620" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">矩阵</text>
  <text x="670" y="595" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">产品矩阵</text>
  <text x="580" y="630" font-family="Microsoft YaHei" font-size="22" fill="#374151">居家/社区</text>
  <text x="580" y="655" font-family="Microsoft YaHei" font-size="22" fill="#374151">机构场景</text>
  <text x="580" y="680" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">全场景覆盖</text>
  
  <rect x="1000" y="560" width="400" height="140" rx="15" fill="white" stroke="#3b82f6" stroke-width="2"/>
  <circle cx="1060" cy="610" r="25" fill="#3b82f6"/>
  <text x="1060" y="620" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">技术</text>
  <text x="1110" y="595" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">技术支撑</text>
  <text x="1020" y="630" font-family="Microsoft YaHei" font-size="22" fill="#374151">云网平台</text>
  <text x="1020" y="655" font-family="Microsoft YaHei" font-size="22" fill="#374151">AI能力</text>
  <text x="1020" y="680" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">技术底座</text>
  
  <rect x="1440" y="560" width="360" height="140" rx="15" fill="white" stroke="#f59e0b" stroke-width="2"/>
  <circle cx="1500" cy="610" r="25" fill="#f59e0b"/>
  <text x="1500" y="620" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">价值</text>
  <text x="1550" y="595" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#92400e">赋能价值</text>
  <text x="1460" y="630" font-family="Microsoft YaHei" font-size="22" fill="#374151">客户价值</text>
  <text x="1460" y="655" font-family="Microsoft YaHei" font-size="22" fill="#374151">商业价值</text>
  <text x="1460" y="680" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">双重价值</text>
  
  <!-- 武器库展示 -->
  <rect x="120" y="750" width="1680" height="200" rx="25" fill="#f8fafc" stroke="#cbd5e1" stroke-width="2"/>
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">电信智慧康养武器库</text>
  
  <!-- 武器图标 -->
  <g transform="translate(300,850)">
    <rect x="-40" y="-20" width="80" height="40" rx="10" fill="#3b82f6"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">智能终端</text>
  </g>
  
  <g transform="translate(500,850)">
    <rect x="-40" y="-20" width="80" height="40" rx="10" fill="#10b981"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">云端平台</text>
  </g>
  
  <g transform="translate(700,850)">
    <rect x="-40" y="-20" width="80" height="40" rx="10" fill="#f59e0b"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">应用服务</text>
  </g>
  
  <g transform="translate(900,850)">
    <rect x="-40" y="-20" width="80" height="40" rx="10" fill="#8b5cf6"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">网络连接</text>
  </g>
  
  <g transform="translate(1100,850)">
    <rect x="-40" y="-20" width="80" height="40" rx="10" fill="#ef4444"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">安全保障</text>
  </g>
  
  <g transform="translate(1300,850)">
    <rect x="-40" y="-20" width="80" height="40" rx="10" fill="#06b6d4"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">运维服务</text>
  </g>
  
  <text x="960" y="920" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#374151">端到端完整解决方案，全方位守护银龄生活</text>
  
  <!-- 准备开始 -->
  <rect x="120" y="980" width="1680" height="80" rx="15" fill="#dc2626"/>
  <text x="960" y="1010" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">准备好深入了解我们的"武器"了吗？</text>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="white">让我们一起探索电信智慧康养的产品世界！</text>
  
  <!-- 装饰元素 - 武器图标 -->
  <g transform="translate(1700,400)">
    <circle cx="0" cy="0" r="40" fill="#dc2626" opacity="0.1"/>
    <rect x="-20" y="-5" width="40" height="10" rx="5" fill="#dc2626"/>
    <circle cx="-25" cy="0" r="8" fill="#dc2626"/>
    <circle cx="25" cy="0" r="8" fill="#dc2626"/>
  </g>
  
  <!-- 装饰云朵 -->
  <g transform="translate(100,400)">
    <ellipse cx="0" cy="0" rx="25" ry="15" fill="#bfdbfe" opacity="0.4"/>
    <ellipse cx="20" cy="-8" rx="18" ry="12" fill="#bfdbfe" opacity="0.4"/>
    <ellipse cx="-18" cy="-5" rx="15" ry="10" fill="#bfdbfe" opacity="0.4"/>
  </g>
</svg>
