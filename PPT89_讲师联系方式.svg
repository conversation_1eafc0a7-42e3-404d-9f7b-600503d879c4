<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#e5e7eb" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">保持联系，随时交流</text>
  
  <!-- 讲师头像区域 -->
  <g transform="translate(960,350)">
    <circle cx="0" cy="0" r="100" fill="#3b82f6"/>
    <circle cx="0" cy="-20" r="40" fill="white"/>
    <circle cx="-15" cy="-30" r="5" fill="#3b82f6"/>
    <circle cx="15" cy="-30" r="5" fill="#3b82f6"/>
    <path d="M-20,-10 Q0,0 20,-10" stroke="#3b82f6" stroke-width="3" fill="none"/>
    <ellipse cx="0" cy="30" rx="60" ry="40" fill="white"/>
    
    <!-- 眼镜装饰 -->
    <circle cx="-15" cy="-30" r="12" fill="none" stroke="#3b82f6" stroke-width="2"/>
    <circle cx="15" cy="-30" r="12" fill="none" stroke="#3b82f6" stroke-width="2"/>
    <path d="M3,-30 L-3,-30" stroke="#3b82f6" stroke-width="2"/>
    
    <text x="0" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">讲师姓名</text>
    <text x="0" y="180" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">智慧康养培训专家</text>
  </g>
  
  <!-- 联系方式 -->
  <rect x="120" y="550" width="1680" height="350" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <text x="160" y="600" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">联系方式</text>
  
  <!-- 联系电话 -->
  <g transform="translate(300,650)">
    <circle cx="0" cy="0" r="40" fill="#10b981"/>
    <circle cx="0" cy="0" r="25" fill="white"/>
    <rect x="-15" y="-15" width="30" height="30" rx="5" fill="#10b981"/>
    <rect x="-10" y="-10" width="20" height="20" rx="3" fill="white"/>
    <circle cx="-5" cy="-5" r="2" fill="#10b981"/>
    <circle cx="5" cy="-5" r="2" fill="#10b981"/>
    <circle cx="-5" cy="5" r="2" fill="#10b981"/>
    <circle cx="5" cy="5" r="2" fill="#10b981"/>
    
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#065f46">联系电话</text>
    <text x="0" y="110" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#374151">138-XXXX-XXXX</text>
  </g>
  
  <!-- 企业微信 -->
  <g transform="translate(700,650)">
    <circle cx="0" cy="0" r="40" fill="#3b82f6"/>
    <circle cx="0" cy="0" r="25" fill="white"/>
    <rect x="-15" y="-15" width="30" height="30" rx="5" fill="#3b82f6"/>
    <rect x="-10" y="-10" width="20" height="20" rx="3" fill="white"/>
    <path d="M-8,-8 L8,-8 M-8,-3 L8,-3 M-8,2 L8,2 M-8,7 L8,7" stroke="#3b82f6" stroke-width="1"/>
    
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">企业微信</text>
    <text x="0" y="110" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#374151">WeChat-ID</text>
  </g>
  
  <!-- 邮箱 -->
  <g transform="translate(1100,650)">
    <circle cx="0" cy="0" r="40" fill="#f59e0b"/>
    <circle cx="0" cy="0" r="25" fill="white"/>
    <rect x="-15" y="-10" width="30" height="20" rx="3" fill="#f59e0b"/>
    <rect x="-12" y="-7" width="24" height="14" rx="2" fill="white"/>
    <path d="M-12,-7 L0,3 L12,-7" stroke="#f59e0b" stroke-width="2" fill="none"/>
    
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#92400e">邮箱</text>
    <text x="0" y="110" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#374151"><EMAIL></text>
  </g>
  
  <!-- 支持承诺 -->
  <rect x="160" y="780" width="1600" height="100" rx="15" fill="#f0f9ff" stroke="#3b82f6" stroke-width="2"/>
  <text x="960" y="820" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">"实战中有任何紧急问题或需要支持，可以随时联系我！"</text>
  <text x="960" y="860" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#374151">7x24小时在线支持，助力您的成长之路</text>
  
  <!-- 服务承诺 -->
  <g transform="translate(960,950)">
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">服务承诺</text>
    
    <rect x="-300" y="0" width="150" height="60" rx="10" fill="#10b981" opacity="0.1" stroke="#10b981" stroke-width="2"/>
    <text x="-225" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#065f46">及时响应</text>
    <text x="-225" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">快速回复</text>
    
    <rect x="-120" y="0" width="150" height="60" rx="10" fill="#3b82f6" opacity="0.1" stroke="#3b82f6" stroke-width="2"/>
    <text x="-45" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#1e3a8a">专业指导</text>
    <text x="-45" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">精准建议</text>
    
    <rect x="60" y="0" width="150" height="60" rx="10" fill="#f59e0b" opacity="0.1" stroke="#f59e0b" stroke-width="2"/>
    <text x="135" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#92400e">持续跟进</text>
    <text x="135" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">全程陪伴</text>
    
    <rect x="240" y="0" width="150" height="60" rx="10" fill="#ef4444" opacity="0.1" stroke="#ef4444" stroke-width="2"/>
    <text x="315" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#dc2626">成长助力</text>
    <text x="315" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#374151">能力提升</text>
  </g>
  
  <!-- 底部装饰 -->
  <rect x="120" y="1050" width="1680" height="20" rx="10" fill="#3b82f6" opacity="0.1"/>
</svg>
