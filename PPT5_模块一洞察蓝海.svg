<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="oceanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#0284c7;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 海洋波浪装饰 -->
  <path d="M0,200 Q480,150 960,200 T1920,200" stroke="#0ea5e9" stroke-width="4" fill="none" opacity="0.6"/>
  <path d="M0,250 Q480,200 960,250 T1920,250" stroke="#0284c7" stroke-width="3" fill="none" opacity="0.4"/>
  <path d="M0,300 Q480,250 960,300 T1920,300" stroke="#0369a1" stroke-width="2" fill="none" opacity="0.3"/>
  
  <!-- 模块标识 -->
  <rect x="120" y="120" width="200" height="80" rx="15" fill="#1e3a8a"/>
  <text x="220" y="170" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">模块一</text>
  
  <!-- 主标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)">洞察蓝海</text>
  <text x="960" y="260" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#374151">看清智慧康养的"危"与"机"</text>
  
  <!-- 模块目标 -->
  <rect x="120" y="350" width="1680" height="120" rx="20" fill="url(#oceanGradient)" stroke="#0ea5e9" stroke-width="2"/>
  <text x="160" y="390" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">模块目标</text>
  <text x="160" y="430" font-family="Microsoft YaHei" font-size="32" fill="#374151">接下来，我们将一起深入市场，看看这片蓝海到底有多大？</text>
  <text x="160" y="460" font-family="Microsoft YaHei" font-size="32" fill="#374151" font-weight="bold">水有多深？我们的船该往哪里开？</text>
  
  <!-- 核心议题 -->
  <text x="120" y="540" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#1e3a8a">核心议题</text>
  
  <!-- 议题卡片 -->
  <rect x="120" y="580" width="320" height="120" rx="15" fill="white" stroke="#3b82f6" stroke-width="2"/>
  <circle cx="180" cy="620" r="20" fill="#3b82f6"/>
  <text x="220" y="610" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">市场背景</text>
  <text x="140" y="650" font-family="Microsoft YaHei" font-size="24" fill="#374151">银发浪潮</text>
  <text x="140" y="680" font-family="Microsoft YaHei" font-size="24" fill="#374151">规模趋势</text>
  
  <rect x="480" y="580" width="320" height="120" rx="15" fill="white" stroke="#10b981" stroke-width="2"/>
  <circle cx="540" cy="620" r="20" fill="#10b981"/>
  <text x="580" y="610" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">需求变化</text>
  <text x="500" y="650" font-family="Microsoft YaHei" font-size="24" fill="#374151">四维升级</text>
  <text x="500" y="680" font-family="Microsoft YaHei" font-size="24" fill="#374151">痛点分析</text>
  
  <rect x="840" y="580" width="320" height="120" rx="15" fill="white" stroke="#f59e0b" stroke-width="2"/>
  <circle cx="900" cy="620" r="20" fill="#f59e0b"/>
  <text x="940" y="610" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#92400e">政策机遇</text>
  <text x="860" y="650" font-family="Microsoft YaHei" font-size="24" fill="#374151">国家战略</text>
  <text x="860" y="680" font-family="Microsoft YaHei" font-size="24" fill="#374151">地方支持</text>
  
  <rect x="1200" y="580" width="320" height="120" rx="15" fill="white" stroke="#8b5cf6" stroke-width="2"/>
  <circle cx="1260" cy="620" r="20" fill="#8b5cf6"/>
  <text x="1300" y="610" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#6b21a8">痛点分析</text>
  <text x="1220" y="650" font-family="Microsoft YaHei" font-size="24" fill="#374151">用户困扰</text>
  <text x="1220" y="680" font-family="Microsoft YaHei" font-size="24" fill="#374151">行业挑战</text>
  
  <rect x="1560" y="580" width="240" height="120" rx="15" fill="white" stroke="#ef4444" stroke-width="2"/>
  <circle cx="1620" cy="620" r="20" fill="#ef4444"/>
  <text x="1660" y="610" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#dc2626">电信定位</text>
  <text x="1580" y="650" font-family="Microsoft YaHei" font-size="24" fill="#374151">优势分析</text>
  <text x="1580" y="680" font-family="Microsoft YaHei" font-size="24" fill="#374151">战略选择</text>
  
  <!-- 探索之旅 -->
  <rect x="120" y="750" width="1680" height="200" rx="25" fill="#f8fafc" stroke="#cbd5e1" stroke-width="2"/>
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">探索之旅开始</text>
  
  <!-- 船只图标 -->
  <g transform="translate(860,820)">
    <path d="M0,40 Q50,20 100,40 L100,60 Q50,80 0,60 Z" fill="#3b82f6"/>
    <rect x="45" y="10" width="10" height="30" fill="#374151"/>
    <path d="M55,10 L55,25 L80,20 Z" fill="#ef4444"/>
    <circle cx="25" cy="50" r="3" fill="white"/>
    <circle cx="75" cy="50" r="3" fill="white"/>
  </g>
  
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#374151">让我们一起扬帆起航，探索智慧康养的蓝海世界！</text>
  <text x="960" y="920" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#1e3a8a" font-weight="bold">准备好了吗？</text>
  
  <!-- 装饰波浪 -->
  <path d="M0,1000 Q480,980 960,1000 T1920,1000 L1920,1080 L0,1080 Z" fill="url(#oceanGradient)"/>
</svg>
