<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="coreGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:0.6" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#bfdbfe" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">找准位置，精准发力：电信的战略选择</text>
  
  <!-- 战略定位靶心图 -->
  <g transform="translate(960,540)">
    <!-- 外圈：目标 -->
    <circle cx="0" cy="0" r="280" fill="#f0f9ff" stroke="#3b82f6" stroke-width="3" opacity="0.3"/>
    <text x="0" y="-250" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">目标</text>
    <text x="0" y="-220" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" fill="#374151">满足客户"安全、健康、便捷、关爱"需求</text>
    <text x="0" y="-190" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" fill="#374151">实现商业价值与社会价值统一</text>
    
    <!-- 中圈：驱动 -->
    <circle cx="0" cy="0" r="200" fill="#dbeafe" stroke="#3b82f6" stroke-width="3" opacity="0.5"/>
    <text x="0" y="-170" text-anchor="middle" font-family="Microsoft YaHei" font-size="30" font-weight="bold" fill="#1e3a8a">驱动</text>
    <text x="-80" y="-140" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">品牌</text>
    <text x="0" y="-140" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">+</text>
    <text x="80" y="-140" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">渠道</text>
    <text x="0" y="-110" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">+</text>
    <text x="0" y="-80" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">服务</text>
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">(市场引擎)</text>
    
    <!-- 内圈：依托 -->
    <circle cx="0" cy="0" r="120" fill="#bfdbfe" stroke="#3b82f6" stroke-width="3" opacity="0.7"/>
    <text x="0" y="-90" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">依托</text>
    <text x="0" y="-60" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#374151">网络+云+平台+安全</text>
    <text x="0" y="-35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">(技术底座)</text>
    
    <!-- 核心：战略定位 -->
    <circle cx="0" cy="0" r="80" fill="url(#coreGradient)" stroke="#dc2626" stroke-width="4"/>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">领先的智慧康养</text>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="white">技术赋能者</text>
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">&amp;</text>
    <text x="0" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">综合服务提供商</text>
    
    <!-- 四个维度标注 -->
    <!-- 安全 -->
    <g transform="translate(-200,-200)">
      <rect x="-60" y="-25" width="120" height="50" rx="25" fill="#ef4444" opacity="0.9"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">安全</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">Safe</text>
    </g>
    
    <!-- 健康 -->
    <g transform="translate(200,-200)">
      <rect x="-60" y="-25" width="120" height="50" rx="25" fill="#10b981" opacity="0.9"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">健康</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">Healthy</text>
    </g>
    
    <!-- 便捷 -->
    <g transform="translate(-200,200)">
      <rect x="-60" y="-25" width="120" height="50" rx="25" fill="#3b82f6" opacity="0.9"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">便捷</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">Convenient</text>
    </g>
    
    <!-- 关爱 -->
    <g transform="translate(200,200)">
      <rect x="-60" y="-25" width="120" height="50" rx="25" fill="#f59e0b" opacity="0.9"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">关爱</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">Caring</text>
    </g>
    
    <!-- 连接线 -->
    <path d="M-80,0 L-140,-140" stroke="#ef4444" stroke-width="2" stroke-dasharray="5,5"/>
    <path d="M80,0 L140,-140" stroke="#10b981" stroke-width="2" stroke-dasharray="5,5"/>
    <path d="M-80,0 L-140,140" stroke="#3b82f6" stroke-width="2" stroke-dasharray="5,5"/>
    <path d="M80,0 L140,140" stroke="#f59e0b" stroke-width="2" stroke-dasharray="5,5"/>
  </g>
  
  <!-- 定位说明 -->
  <rect x="120" y="200" width="600" height="200" rx="20" fill="white" stroke="#1e3a8a" stroke-width="2"/>
  <text x="140" y="240" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">我们的定位</text>
  <text x="140" y="280" font-family="Microsoft YaHei" font-size="24" fill="#374151">不是简单的卖设备商</text>
  <text x="140" y="310" font-family="Microsoft YaHei" font-size="24" fill="#374151">也不是纯粹的服务提供商</text>
  <text x="140" y="350" font-family="Microsoft YaHei" font-size="26" fill="#374151" font-weight="bold">而是技术+服务双轮驱动的</text>
  <text x="140" y="380" font-family="Microsoft YaHei" font-size="26" fill="#374151" font-weight="bold">综合解决方案提供者和平台运营者</text>
  
  <!-- 核心能力 -->
  <rect x="1200" y="200" width="600" height="200" rx="20" fill="white" stroke="#10b981" stroke-width="2"/>
  <text x="1220" y="240" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">核心能力</text>
  <text x="1220" y="280" font-family="Microsoft YaHei" font-size="24" fill="#374151">✓ 技术赋能：5G+云+AI+IoT</text>
  <text x="1220" y="310" font-family="Microsoft YaHei" font-size="24" fill="#374151">✓ 平台整合：统一接入，数据互通</text>
  <text x="1220" y="340" font-family="Microsoft YaHei" font-size="24" fill="#374151">✓ 服务保障：全国网络，专业团队</text>
  <text x="1220" y="370" font-family="Microsoft YaHei" font-size="24" fill="#374151">✓ 生态构建：开放合作，共赢发展</text>
  
  <!-- 讲解要点 -->
  <rect x="120" y="880" width="1680" height="120" rx="20" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="920" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">讲解要点</text>
  <text x="160" y="955" font-family="Microsoft YaHei" font-size="28" fill="#374151">我们的定位不是简单的卖设备商，也不是纯粹的服务提供商，</text>
  <text x="160" y="985" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">而是技术+服务双轮驱动的综合解决方案提供者和平台运营者。这是我们的目标和方向。</text>
  
  <!-- 愿景强调 -->
  <rect x="120" y="1020" width="1680" height="50" rx="15" fill="#dc2626"/>
  <text x="960" y="1050" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">用科技守护银龄生活，让每一位老人都能安享晚年！</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(100,500)">
    <circle cx="0" cy="0" r="30" fill="#3b82f6" opacity="0.1"/>
    <path d="M-15,-15 L0,0 L15,-15 M0,0 L0,20" stroke="#3b82f6" stroke-width="3" fill="none"/>
  </g>
  
  <g transform="translate(1820,500)">
    <circle cx="0" cy="0" r="30" fill="#10b981" opacity="0.1"/>
    <circle cx="0" cy="0" r="15" fill="#10b981" opacity="0.2"/>
    <path d="M-8,-8 L8,8 M8,-8 L-8,8" stroke="#10b981" stroke-width="2"/>
  </g>
</svg>
