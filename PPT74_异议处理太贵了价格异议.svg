<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fef2f2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#fecaca" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">常见异议处理①："太贵了！" (价格异议)</text>
  
  <!-- 错误应对 -->
  <rect x="120" y="200" width="1680" height="100" rx="20" fill="#fef2f2" stroke="#ef4444" stroke-width="3"/>
  <circle cx="220" cy="240" r="30" fill="#ef4444"/>
  <path d="M205,225 L235,255 M235,225 L205,255" stroke="white" stroke-width="4"/>
  
  <text x="280" y="235" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#dc2626">错误应对</text>
  <text x="280" y="270" font-family="Microsoft YaHei" font-size="28" fill="#374151">"我们这是最低价了！" / "一分钱一分货！" </text>
  <text x="1200" y="270" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">(容易陷入对抗)</text>
  
  <!-- LACE步骤 -->
  <text x="120" y="350" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#dc2626">LACE 步骤</text>
  
  <!-- L&A -->
  <rect x="120" y="380" width="800" height="120" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="220" cy="430" r="40" fill="#10b981"/>
  <text x="220" y="440" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">L&amp;A</text>
  
  <text x="300" y="415" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">L&amp;A (倾听&amp;承认)</text>
  <text x="300" y="450" font-family="Microsoft YaHei" font-size="24" fill="#374151">"嗯，价格确实是大家都会考虑的重要因素，</text>
  <text x="300" y="480" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">我理解。"</text>
  
  <!-- C -->
  <rect x="1000" y="380" width="800" height="120" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="1100" cy="430" r="40" fill="#3b82f6"/>
  <text x="1100" y="440" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">C</text>
  
  <text x="1180" y="405" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">C (澄清)</text>
  <text x="1180" y="435" font-family="Microsoft YaHei" font-size="22" fill="#374151">"为了更好地给您建议，方便了解一下，</text>
  <text x="1180" y="460" font-family="Microsoft YaHei" font-size="22" fill="#374151">您觉得'贵'是和什么在比较呢？"</text>
  <text x="1180" y="485" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">"您大概的预算范围是多少？"</text>
  
  <!-- E - 重塑价值 -->
  <rect x="120" y="520" width="1680" height="300" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="220" cy="620" r="40" fill="#f59e0b"/>
  <text x="220" y="630" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">E</text>
  
  <text x="300" y="560" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">E (重塑价值)</text>
  
  <!-- 算ROI -->
  <rect x="300" y="580" width="350" height="120" rx="15" fill="#10b981" opacity="0.1" stroke="#10b981" stroke-width="2"/>
  <text x="320" y="605" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#065f46">算ROI：</text>
  <text x="320" y="630" font-family="Microsoft YaHei" font-size="18" fill="#374151">"我们帮您算笔账，</text>
  <text x="320" y="650" font-family="Microsoft YaHei" font-size="18" fill="#374151">这个投入每年能节省/</text>
  <text x="320" y="670" font-family="Microsoft YaHei" font-size="18" fill="#374151">带来..."</text>
  <text x="320" y="690" font-family="Microsoft YaHei" font-size="16" fill="#6b7280">量化收益</text>
  
  <!-- 强调独特价值 -->
  <rect x="680" y="580" width="350" height="120" rx="15" fill="#3b82f6" opacity="0.1" stroke="#3b82f6" stroke-width="2"/>
  <text x="700" y="605" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1e3a8a">强调独特价值：</text>
  <text x="700" y="630" font-family="Microsoft YaHei" font-size="18" fill="#374151">"我们的优势在于</text>
  <text x="700" y="650" font-family="Microsoft YaHei" font-size="18" fill="#374151">7x24小时服务/品牌保障/</text>
  <text x="700" y="670" font-family="Microsoft YaHei" font-size="18" fill="#374151">数据安全，这是无法单用</text>
  <text x="700" y="690" font-family="Microsoft YaHei" font-size="18" fill="#374151">价格衡量的..."</text>
  
  <!-- 拆分价值 -->
  <rect x="1060" y="580" width="350" height="120" rx="15" fill="#f59e0b" opacity="0.1" stroke="#f59e0b" stroke-width="2"/>
  <text x="1080" y="605" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#92400e">拆分价值：</text>
  <text x="1080" y="630" font-family="Microsoft YaHei" font-size="18" fill="#374151">"您可以先从这个</text>
  <text x="1080" y="650" font-family="Microsoft YaHei" font-size="18" fill="#374151">基础包开始，解决您</text>
  <text x="1080" y="670" font-family="Microsoft YaHei" font-size="18" fill="#374151">最核心的安全问题..."</text>
  <text x="1080" y="690" font-family="Microsoft YaHei" font-size="16" fill="#6b7280">分步实施</text>
  
  <!-- 案例证明 -->
  <rect x="1440" y="580" width="320" height="120" rx="15" fill="#ef4444" opacity="0.1" stroke="#ef4444" stroke-width="2"/>
  <text x="1460" y="605" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#dc2626">案例证明：</text>
  <text x="1460" y="630" font-family="Microsoft YaHei" font-size="18" fill="#374151">"我们有个客户</text>
  <text x="1460" y="650" font-family="Microsoft YaHei" font-size="18" fill="#374151">之前也觉得贵，</text>
  <text x="1460" y="670" font-family="Microsoft YaHei" font-size="18" fill="#374151">但用了之后..."</text>
  <text x="1460" y="690" font-family="Microsoft YaHei" font-size="16" fill="#6b7280">客户证言</text>
  
  <!-- 价值重塑公式 -->
  <g transform="translate(960,900)">
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#dc2626">价值重塑公式</text>
    
    <rect x="-350" y="0" width="150" height="80" rx="15" fill="#10b981"/>
    <text x="-275" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">量化收益</text>
    <text x="-275" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">算ROI</text>
    <text x="-275" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">具体数字</text>
    
    <text x="-170" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#374151">+</text>
    
    <rect x="-120" y="0" width="150" height="80" rx="15" fill="#3b82f6"/>
    <text x="-45" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">独特价值</text>
    <text x="-45" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">差异化</text>
    <text x="-45" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">不可替代</text>
    
    <text x="80" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#374151">+</text>
    
    <rect x="130" y="0" width="150" height="80" rx="15" fill="#ef4444"/>
    <text x="205" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">客户证言</text>
    <text x="205" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">成功案例</text>
    <text x="205" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">口碑背书</text>
  </g>
  
  <!-- 讲解要点 -->
  <rect x="120" y="1000" width="1680" height="70" rx="15" fill="#dc2626" opacity="0.1"/>
  <text x="160" y="1030" font-family="Microsoft YaHei" font-size="28" fill="#374151">"永远不要只谈价格，</text>
  <text x="160" y="1060" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">要把焦点拉回到价值！"</text>
</svg>
