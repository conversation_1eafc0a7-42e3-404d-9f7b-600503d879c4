<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f87171;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#bfdbfe" stroke-width="3" fill="none"/>
  <path d="M0,960 Q960,1000 1920,960" stroke="#bfdbfe" stroke-width="3" fill="none"/>
  
  <!-- 主标题 -->
  <text x="960" y="300" text-anchor="middle" font-family="Microsoft YaHei" font-size="120" font-weight="bold" fill="url(#titleGradient)">谢谢大家！</text>
  
  <!-- 英文标题 -->
  <text x="960" y="380" text-anchor="middle" font-family="Arial" font-size="48" font-weight="bold" fill="#6b7280">Thank You!</text>
  
  <!-- 感谢的心 -->
  <g transform="translate(960,540)">
    <!-- 大心形 -->
    <path d="M0,50 C-50,0 -100,0 -100,50 C-100,100 0,150 0,200 C0,150 100,100 100,50 C100,0 50,0 0,50 Z" fill="url(#heartGradient)"/>
    
    <!-- 小心形装饰 -->
    <g transform="translate(-200,-50)">
      <path d="M0,20 C-20,0 -40,0 -40,20 C-40,40 0,60 0,80 C0,60 40,40 40,20 C40,0 20,0 0,20 Z" fill="#f59e0b" opacity="0.7"/>
    </g>
    
    <g transform="translate(200,-50)">
      <path d="M0,20 C-20,0 -40,0 -40,20 C-40,40 0,60 0,80 C0,60 40,40 40,20 C40,0 20,0 0,20 Z" fill="#10b981" opacity="0.7"/>
    </g>
    
    <g transform="translate(-150,100)">
      <path d="M0,15 C-15,0 -30,0 -30,15 C-30,30 0,45 0,60 C0,45 30,30 30,15 C30,0 15,0 0,15 Z" fill="#3b82f6" opacity="0.7"/>
    </g>
    
    <g transform="translate(150,100)">
      <path d="M0,15 C-15,0 -30,0 -30,15 C-30,30 0,45 0,60 C0,45 30,30 30,15 C30,0 15,0 0,15 Z" fill="#8b5cf6" opacity="0.7"/>
    </g>
    
    <!-- 中心文字 -->
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">感谢</text>
  </g>
  
  <!-- 祝福语 -->
  <rect x="120" y="750" width="1680" height="150" rx="30" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <text x="960" y="810" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#1e3a8a">祝愿大家在接下来的实战中</text>
  <text x="960" y="870" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#1e3a8a">收获满满，成长飞跃！</text>
  
  <!-- 装饰星星 -->
  <g transform="translate(200,200)">
    <path d="M0,-20 L5,-5 L20,-5 L10,5 L15,20 L0,10 L-15,20 L-10,5 L-20,-5 L-5,-5 Z" fill="#f59e0b" opacity="0.6"/>
  </g>
  
  <g transform="translate(1720,200)">
    <path d="M0,-20 L5,-5 L20,-5 L10,5 L15,20 L0,10 L-15,20 L-10,5 L-20,-5 L-5,-5 Z" fill="#10b981" opacity="0.6"/>
  </g>
  
  <g transform="translate(150,900)">
    <path d="M0,-15 L4,-4 L15,-4 L7,4 L11,15 L0,8 L-11,15 L-7,4 L-15,-4 L-4,-4 Z" fill="#3b82f6" opacity="0.6"/>
  </g>
  
  <g transform="translate(1770,900)">
    <path d="M0,-15 L4,-4 L15,-4 L7,4 L11,15 L0,8 L-11,15 L-7,4 L-15,-4 L-4,-4 Z" fill="#ef4444" opacity="0.6"/>
  </g>
  
  <!-- 底部标语 -->
  <text x="960" y="1000" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#3b82f6">智慧康养，共创未来</text>
  <text x="960" y="1040" text-anchor="middle" font-family="Arial" font-size="24" fill="#6b7280">Smart Healthcare, Bright Future Together</text>
</svg>
