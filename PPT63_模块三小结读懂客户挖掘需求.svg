<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#e5e7eb" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">读懂客户，挖掘需求小结</text>
  
  <!-- 小结内容 -->
  <text x="960" y="220" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#6b7280">模块三小结 &amp; Q&amp;A</text>
  
  <!-- 知客 -->
  <rect x="120" y="280" width="1680" height="120" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="200" cy="330" r="40" fill="#10b981"/>
  <text x="200" y="340" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">知客</text>
  
  <text x="280" y="315" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">知客：细分是前提，画像是工具</text>
  <text x="280" y="355" font-family="Microsoft YaHei" font-size="28" fill="#374151">B端/C端差异化分析，Persona让客户"活"起来</text>
  <text x="280" y="380" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">不同客户有不同的痛点、需求和决策逻辑</text>
  
  <!-- 探需 -->
  <rect x="120" y="420" width="1680" height="120" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="200" cy="470" r="40" fill="#3b82f6"/>
  <text x="200" y="480" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">探需</text>
  
  <text x="280" y="455" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">探需：SPIN是利器，I&amp;N是核心</text>
  <text x="280" y="495" font-family="Microsoft YaHei" font-size="28" fill="#374151">结构化提问，挖掘深层需求</text>
  <text x="280" y="520" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">让客户自己说出问题的严重性和解决方案的价值</text>
  
  <!-- 关键 -->
  <rect x="120" y="560" width="1680" height="120" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="200" cy="610" r="40" fill="#f59e0b"/>
  <text x="200" y="620" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">关键</text>
  
  <text x="280" y="595" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">关键：问对话，听懂话，才能做到点子上！</text>
  <text x="280" y="635" font-family="Microsoft YaHei" font-size="28" fill="#374151">80%时间听，20%时间问</text>
  <text x="280" y="660" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">倾听比说话更重要，理解比推销更有效</text>
  
  <!-- SPIN核心要点回顾 -->
  <g transform="translate(960,750)">
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">SPIN核心要点回顾</text>
    
    <!-- S -->
    <rect x="-400" y="0" width="150" height="80" rx="15" fill="#10b981"/>
    <text x="-325" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">S - 现状</text>
    <text x="-325" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">了解背景</text>
    <text x="-325" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">建立信任</text>
    
    <!-- P -->
    <rect x="-220" y="0" width="150" height="80" rx="15" fill="#3b82f6"/>
    <text x="-145" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">P - 问题</text>
    <text x="-145" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">发现痛点</text>
    <text x="-145" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">引导表达</text>
    
    <!-- I -->
    <rect x="-40" y="0" width="150" height="80" rx="15" fill="#ef4444"/>
    <text x="35" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">I - 影响</text>
    <text x="35" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">放大后果</text>
    <text x="35" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">感受痛苦</text>
    
    <!-- N -->
    <rect x="140" y="0" width="150" height="80" rx="15" fill="#f59e0b"/>
    <text x="215" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">N - 价值</text>
    <text x="215" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">聚焦收益</text>
    <text x="215" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">渴望解脱</text>
  </g>
  
  <!-- 互动环节 -->
  <rect x="120" y="850" width="1680" height="120" rx="20" fill="#f8fafc" stroke="#cbd5e1" stroke-width="2"/>
  <text x="160" y="890" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">互动环节</text>
  <text x="160" y="930" font-family="Microsoft YaHei" font-size="32" fill="#374151">"关于客户分析和SPIN，大家还有哪些疑问或者觉得困难的地方？"</text>
  <text x="160" y="955" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">预留5分钟进行Q&amp;A互动</text>
  
  <!-- 过渡提示 -->
  <rect x="120" y="990" width="1680" height="80" rx="15" fill="#10b981"/>
  <text x="960" y="1020" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">模块三完成！掌握了客户分析和需求挖掘的核心技能</text>
  <text x="960" y="1050" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#dcfce7">接下来的实战中，请大家多加练习，熟能生巧！</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(100,500)">
    <circle cx="0" cy="0" r="25" fill="#10b981" opacity="0.2"/>
    <circle cx="0" cy="0" r="15" fill="#10b981" opacity="0.4"/>
    <circle cx="0" cy="0" r="8" fill="#10b981"/>
  </g>
  
  <g transform="translate(1820,500)">
    <circle cx="0" cy="0" r="25" fill="#3b82f6" opacity="0.2"/>
    <path d="M-12,-12 L0,0 L12,-12 M0,0 L0,15" stroke="#3b82f6" stroke-width="3" fill="none"/>
  </g>
</svg>
