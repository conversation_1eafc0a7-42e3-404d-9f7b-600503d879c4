<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fef2f2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#fecaca" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">核心需求聚焦一：安全 - 老人和子女最大的"定心丸"</text>
  
  <!-- 痛点场景展示 -->
  <text x="120" y="240" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#dc2626">典型痛点场景</text>
  
  <!-- 场景1：摔倒 -->
  <rect x="120" y="280" width="400" height="200" rx="20" fill="white" stroke="#ef4444" stroke-width="2"/>
  <circle cx="220" cy="350" r="50" fill="#fecaca"/>
  <!-- 摔倒老人剪影 -->
  <g transform="translate(220,350)">
    <ellipse cx="0" cy="-20" rx="15" ry="20" fill="#dc2626"/>
    <rect x="-10" y="0" width="20" height="30" fill="#dc2626"/>
    <rect x="-15" y="30" width="10" height="20" fill="#dc2626" transform="rotate(-30)"/>
    <rect x="5" y="30" width="10" height="20" fill="#dc2626" transform="rotate(30)"/>
    <rect x="-20" y="5" width="15" height="8" fill="#dc2626" transform="rotate(-45)"/>
    <rect x="5" y="5" width="15" height="8" fill="#dc2626" transform="rotate(45)"/>
  </g>
  <text x="300" y="330" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#dc2626">意外摔倒</text>
  <text x="140" y="420" font-family="Microsoft YaHei" font-size="24" fill="#374151">老人在家摔倒</text>
  <text x="140" y="450" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">无人知晓，错失救援</text>
  
  <!-- 场景2：燃气风险 -->
  <rect x="560" y="280" width="400" height="200" rx="20" fill="white" stroke="#ef4444" stroke-width="2"/>
  <circle cx="660" cy="350" r="50" fill="#fecaca"/>
  <!-- 燃气灶图标 -->
  <g transform="translate(660,350)">
    <rect x="-25" y="10" width="50" height="20" rx="5" fill="#dc2626"/>
    <circle cx="-15" cy="0" r="8" fill="#f59e0b"/>
    <circle cx="0" cy="0" r="8" fill="#f59e0b"/>
    <circle cx="15" cy="0" r="8" fill="#f59e0b"/>
    <path d="M-15,-15 Q-15,-25 -10,-20 Q-5,-25 0,-20 Q5,-25 10,-20 Q15,-25 15,-15" stroke="#f59e0b" stroke-width="2" fill="none"/>
  </g>
  <text x="740" y="330" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#dc2626">燃气风险</text>
  <text x="580" y="420" font-family="Microsoft YaHei" font-size="24" fill="#374151">忘记关火、漏气</text>
  <text x="580" y="450" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">安全隐患巨大</text>
  
  <!-- 场景3：走失 -->
  <rect x="1000" y="280" width="400" height="200" rx="20" fill="white" stroke="#ef4444" stroke-width="2"/>
  <circle cx="1100" cy="350" r="50" fill="#fecaca"/>
  <!-- 迷路图标 -->
  <g transform="translate(1100,350)">
    <circle cx="0" cy="-15" r="12" fill="#dc2626"/>
    <rect x="-8" y="-3" width="16" height="25" fill="#dc2626"/>
    <rect x="-12" y="22" width="8" height="15" fill="#dc2626"/>
    <rect x="4" y="22" width="8" height="15" fill="#dc2626"/>
    <path d="M-20,-10 Q-30,-20 -25,-5 M20,-10 Q30,-20 25,-5" stroke="#dc2626" stroke-width="3" fill="none"/>
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#dc2626">?</text>
  </g>
  <text x="1180" y="330" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#dc2626">走失风险</text>
  <text x="1020" y="420" font-family="Microsoft YaHei" font-size="24" fill="#374151">认知障碍老人</text>
  <text x="1020" y="450" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">外出迷路走失</text>
  
  <!-- 场景4：子女焦虑 -->
  <rect x="1440" y="280" width="360" height="200" rx="20" fill="white" stroke="#ef4444" stroke-width="2"/>
  <circle cx="1540" cy="350" r="50" fill="#fecaca"/>
  <!-- 焦虑表情 -->
  <g transform="translate(1540,350)">
    <circle cx="0" cy="0" r="30" fill="#dc2626"/>
    <circle cx="-10" cy="-8" r="4" fill="white"/>
    <circle cx="10" cy="-8" r="4" fill="white"/>
    <path d="M-15,10 Q0,25 15,10" stroke="white" stroke-width="3" fill="none"/>
    <path d="M-20,-20 Q-15,-25 -10,-20 M10,-20 Q15,-25 20,-20" stroke="white" stroke-width="2" fill="none"/>
  </g>
  <text x="1620" y="330" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#dc2626">子女焦虑</text>
  <text x="1460" y="420" font-family="Microsoft YaHei" font-size="24" fill="#374151">时刻担心</text>
  <text x="1460" y="450" font-family="Microsoft YaHei" font-size="24" fill="#374151" font-weight="bold">父母安危</text>
  
  <!-- 痛点总结 -->
  <rect x="120" y="520" width="1680" height="100" rx="15" fill="#dc2626" opacity="0.1"/>
  <text x="160" y="560" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#dc2626">核心痛点</text>
  <text x="160" y="595" font-family="Microsoft YaHei" font-size="28" fill="#374151">意外摔倒/急病无人知？ | 居家环境风险？ | 走失风险？ | 子女时刻担忧？</text>
  
  <!-- 解决方案引出 -->
  <rect x="120" y="660" width="1680" height="200" rx="25" fill="#10b981"/>
  <text x="960" y="720" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="white">这些都是高频痛点！</text>
  <text x="960" y="780" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" fill="white">我们的智能安防产品就是来解决这些问题的！</text>
  
  <!-- 解决方案预览 -->
  <g transform="translate(960,820)">
    <rect x="-200" y="0" width="120" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="-140" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#10b981">SOS报警</text>
    
    <rect x="-60" y="0" width="120" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#10b981">环境监测</text>
    
    <rect x="80" y="0" width="120" height="60" rx="10" fill="white" opacity="0.9"/>
    <text x="140" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#10b981">定位守护</text>
  </g>
  
  <!-- 价值主张 -->
  <rect x="120" y="920" width="1680" height="80" rx="15" fill="#1e3a8a"/>
  <text x="960" y="970" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">安全是基石，是老人和子女的最大"定心丸"！</text>
  
  <!-- 装饰元素 -->
  <circle cx="80" cy="600" r="20" fill="#ef4444" opacity="0.2"/>
  <circle cx="1840" cy="600" r="20" fill="#ef4444" opacity="0.2"/>
</svg>
