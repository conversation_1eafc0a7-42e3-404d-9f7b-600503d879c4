<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fef3c7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#92400e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#fde68a" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">C端客户地图：使用者 vs. 购买者，需求各不同</text>
  
  <!-- 使用者 (老人) -->
  <rect x="120" y="200" width="800" height="400" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="220" cy="320" r="60" fill="#10b981"/>
  <circle cx="220" cy="300" r="25" fill="white"/>
  <circle cx="215" cy="290" r="3" fill="#10b981"/>
  <circle cx="225" cy="290" r="3" fill="#10b981"/>
  <path d="M210,310 Q220,320 230,310" stroke="#10b981" stroke-width="2" fill="none"/>
  <ellipse cx="220" cy="340" rx="30" ry="20" fill="white"/>
  <rect x="205" y="360" width="12" height="25" rx="5" fill="white"/>
  <rect x="223" y="360" width="12" height="25" rx="5" fill="white"/>
  
  <text x="320" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#065f46">使用者 (老人)</text>
  
  <!-- 按健康分层 -->
  <text x="320" y="290" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#374151">按健康分层：</text>
  
  <rect x="320" y="310" width="200" height="60" rx="10" fill="#10b981" opacity="0.1" stroke="#10b981" stroke-width="1"/>
  <text x="330" y="330" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#065f46">活力老人</text>
  <text x="330" y="350" font-family="Microsoft YaHei" font-size="18" fill="#374151">(便捷/娱乐)</text>
  <text x="330" y="365" font-family="Microsoft YaHei" font-size="16" fill="#6b7280">追求生活品质</text>
  
  <rect x="540" y="310" width="200" height="60" rx="10" fill="#f59e0b" opacity="0.1" stroke="#f59e0b" stroke-width="1"/>
  <text x="550" y="330" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#92400e">半失能</text>
  <text x="550" y="350" font-family="Microsoft YaHei" font-size="18" fill="#374151">(安全/健康)</text>
  <text x="550" y="365" font-family="Microsoft YaHei" font-size="16" fill="#6b7280">需要适度帮助</text>
  
  <rect x="320" y="390" width="200" height="60" rx="10" fill="#ef4444" opacity="0.1" stroke="#ef4444" stroke-width="1"/>
  <text x="330" y="410" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#dc2626">失能</text>
  <text x="330" y="430" font-family="Microsoft YaHei" font-size="18" fill="#374151">(监护)</text>
  <text x="330" y="445" font-family="Microsoft YaHei" font-size="16" fill="#6b7280">需要全面照护</text>
  
  <!-- 老人痛点 -->
  <text x="320" y="485" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#065f46">痛点：</text>
  <text x="320" y="510" font-family="Microsoft YaHei" font-size="22" fill="#374151">怕麻烦、学不会、担心隐私、孤独</text>
  
  <text x="320" y="540" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#065f46">关注：</text>
  <text x="320" y="565" font-family="Microsoft YaHei" font-size="22" fill="#374151">简单易用、可靠、不打扰、有乐趣</text>
  
  <!-- 购买者 (子女) -->
  <rect x="1000" y="200" width="800" height="400" rx="20" fill="white" stroke="#ef4444" stroke-width="3"/>
  <circle cx="1100" cy="320" r="60" fill="#ef4444"/>
  <circle cx="1100" cy="300" r="25" fill="white"/>
  <circle cx="1095" cy="290" r="3" fill="#ef4444"/>
  <circle cx="1105" cy="290" r="3" fill="#ef4444"/>
  <path d="M1090,310 Q1100,320 1110,310" stroke="#ef4444" stroke-width="2" fill="none"/>
  <ellipse cx="1100" cy="340" rx="30" ry="20" fill="white"/>
  <rect x="1085" y="360" width="12" height="25" rx="5" fill="white"/>
  <rect x="1103" y="360" width="12" height="25" rx="5" fill="white"/>
  <!-- 手机 -->
  <rect x="1130" y="300" width="15" height="25" rx="3" fill="#3b82f6"/>
  <circle cx="1137" cy="307" r="2" fill="white"/>
  
  <text x="1200" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#dc2626">购买者 (子女 - 核心决策者!)</text>
  
  <!-- 子女痛点 -->
  <text x="1200" y="300" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#dc2626">痛点：</text>
  <text x="1200" y="325" font-family="Microsoft YaHei" font-size="22" fill="#374151" font-weight="bold">担心父母安全! 不了解健康、工作忙碌、</text>
  <text x="1200" y="350" font-family="Microsoft YaHei" font-size="22" fill="#374151" font-weight="bold">照护压力大、内心愧疚</text>
  
  <text x="1200" y="385" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#dc2626">关注：</text>
  <text x="1200" y="410" font-family="Microsoft YaHei" font-size="22" fill="#374151" font-weight="bold">安全第一! 可靠! 服务及时! 数据共享!</text>
  <text x="1200" y="435" font-family="Microsoft YaHei" font-size="22" fill="#374151" font-weight="bold">操作简单(给父母用)! 品牌信誉!</text>
  
  <!-- 决策影响因素 -->
  <rect x="1200" y="460" width="580" height="120" rx="15" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <text x="1220" y="490" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#dc2626">决策影响因素：</text>
  <text x="1220" y="520" font-family="Microsoft YaHei" font-size="20" fill="#374151">1. 安全保障能力 (最重要)</text>
  <text x="1220" y="545" font-family="Microsoft YaHei" font-size="20" fill="#374151">2. 服务响应速度</text>
  <text x="1220" y="570" font-family="Microsoft YaHei" font-size="20" fill="#374151">3. 品牌可信度</text>
  
  <!-- 双重挑战 -->
  <rect x="120" y="620" width="1680" height="150" rx="20" fill="#f8fafc" stroke="#cbd5e1" stroke-width="2"/>
  <text x="160" y="660" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">双重挑战</text>
  <text x="160" y="700" font-family="Microsoft YaHei" font-size="28" fill="#374151">既要让老人愿意用、用得好，又要让子女放心买、买得值</text>
  <text x="160" y="735" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">产品设计要考虑使用者体验，营销策略要针对购买者痛点</text>
  <text x="160" y="760" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">沟通时要同时照顾两个群体的关注点</text>
  
  <!-- 沟通策略 -->
  <g transform="translate(960,850)">
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">沟通策略差异</text>
    
    <!-- 对老人 -->
    <rect x="-400" y="0" width="350" height="100" rx="15" fill="#10b981" opacity="0.1" stroke="#10b981" stroke-width="2"/>
    <text x="-225" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#065f46">对老人沟通</text>
    <text x="-225" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">强调：简单、好用、不麻烦</text>
    <text x="-225" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">语言：温和、耐心、通俗</text>
    <text x="-225" y="90" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">演示：现场体验、操作简单</text>
    
    <!-- 对子女 -->
    <rect x="50" y="0" width="350" height="100" rx="15" fill="#ef4444" opacity="0.1" stroke="#ef4444" stroke-width="2"/>
    <text x="225" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#dc2626">对子女沟通</text>
    <text x="225" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">强调：安全、可靠、专业</text>
    <text x="225" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">语言：数据、案例、保障</text>
    <text x="225" y="90" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">演示：功能全面、服务体系</text>
  </g>
  
  <!-- 讲解要点 -->
  <rect x="120" y="970" width="1680" height="100" rx="20" fill="#92400e" opacity="0.1"/>
  <text x="160" y="1005" font-family="Microsoft YaHei" font-size="28" fill="#374151">搞定子女是关键！他们的焦虑和需求是我们沟通的重点。</text>
  <text x="160" y="1035" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">同时也要兼顾老人的使用体验。</text>
  <text x="160" y="1060" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">双管齐下，才能真正成功</text>
</svg>
