<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#bfdbfe" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">"智家孝老"利器①：智能安防 (基础保障)</text>
  
  <!-- SOS一键报警 -->
  <rect x="120" y="200" width="800" height="200" rx="20" fill="white" stroke="#ef4444" stroke-width="3"/>
  <circle cx="220" cy="280" r="50" fill="#ef4444"/>
  <text x="220" y="290" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">SOS</text>
  
  <text x="300" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#dc2626">SOS一键报警</text>
  <text x="300" y="290" font-family="Microsoft YaHei" font-size="28" fill="#374151">形态：按钮/手环/语音</text>
  <text x="300" y="325" font-family="Microsoft YaHei" font-size="26" fill="#374151">解决：紧急情况快速求助</text>
  <text x="300" y="360" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ef4444">价值："救命毫秒间！"</text>
  <text x="300" y="385" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">一键直达家人/社区/120</text>
  
  <!-- 环境安全监测 -->
  <rect x="1000" y="200" width="800" height="200" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="1100" cy="280" r="50" fill="#f59e0b"/>
  <text x="1100" y="290" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">环境</text>
  
  <text x="1180" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#92400e">环境安全监测</text>
  <text x="1180" y="290" font-family="Microsoft YaHei" font-size="28" fill="#374151">烟感/燃气/水浸/门磁</text>
  <text x="1180" y="325" font-family="Microsoft YaHei" font-size="26" fill="#374151">解决：火灾/燃气/漏水/闯入风险</text>
  <text x="1180" y="360" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#f59e0b">价值："防患于未然！"</text>
  <text x="1180" y="385" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">24小时智能监测预警</text>
  
  <!-- 传感器图标展示 -->
  <g transform="translate(960,450)">
    <!-- 烟感 -->
    <circle cx="-150" cy="0" r="30" fill="#ef4444" opacity="0.2"/>
    <circle cx="-150" cy="0" r="15" fill="#ef4444"/>
    <path d="M-160,-10 Q-150,-20 -140,-10 M-160,10 Q-150,20 -140,10" stroke="white" stroke-width="2" fill="none"/>
    <text x="-150" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">烟感</text>
    
    <!-- 燃气 -->
    <circle cx="-50" cy="0" r="30" fill="#f59e0b" opacity="0.2"/>
    <rect x="-65" y="-5" width="30" height="10" rx="2" fill="#f59e0b"/>
    <circle cx="-60" cy="-15" r="4" fill="#ef4444"/>
    <circle cx="-50" cy="-15" r="4" fill="#ef4444"/>
    <circle cx="-40" cy="-15" r="4" fill="#ef4444"/>
    <text x="-50" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">燃气</text>
    
    <!-- 水浸 -->
    <circle cx="50" cy="0" r="30" fill="#06b6d4" opacity="0.2"/>
    <path d="M35,-10 Q50,-20 65,-10 Q50,10 35,-10" fill="#06b6d4"/>
    <path d="M40,5 Q50,15 60,5" fill="#0891b2"/>
    <text x="50" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">水浸</text>
    
    <!-- 门磁 -->
    <circle cx="150" cy="0" r="30" fill="#8b5cf6" opacity="0.2"/>
    <rect x="135" y="-15" width="30" height="30" rx="3" fill="#8b5cf6"/>
    <rect x="140" y="-10" width="20" height="20" rx="2" fill="white"/>
    <circle cx="150" cy="0" r="3" fill="#8b5cf6"/>
    <text x="150" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">门磁</text>
  </g>
  
  <!-- 工作流程 -->
  <rect x="120" y="520" width="1680" height="150" rx="20" fill="#f8fafc" stroke="#cbd5e1" stroke-width="2"/>
  <text x="160" y="560" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">工作流程</text>
  
  <!-- 流程步骤 -->
  <g transform="translate(300,600)">
    <!-- 步骤1 -->
    <rect x="0" y="-25" width="120" height="50" rx="25" fill="#3b82f6"/>
    <text x="60" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">传感器检测</text>
    
    <!-- 箭头1 -->
    <path d="M140,0 L180,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 步骤2 -->
    <rect x="200" y="-25" width="120" height="50" rx="25" fill="#10b981"/>
    <text x="260" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">云端分析</text>
    
    <!-- 箭头2 -->
    <path d="M340,0 L380,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 步骤3 -->
    <rect x="400" y="-25" width="120" height="50" rx="25" fill="#f59e0b"/>
    <text x="460" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">智能预警</text>
    
    <!-- 箭头3 -->
    <path d="M540,0 L580,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 步骤4 -->
    <rect x="600" y="-25" width="120" height="50" rx="25" fill="#ef4444"/>
    <text x="660" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">应急响应</text>
    
    <!-- 箭头4 -->
    <path d="M740,0 L780,0" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 步骤5 -->
    <rect x="800" y="-25" width="120" height="50" rx="25" fill="#8b5cf6"/>
    <text x="860" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">服务到达</text>
  </g>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280"/>
    </marker>
  </defs>
  
  <!-- 讲解要点 -->
  <rect x="120" y="700" width="1680" height="120" rx="20" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="740" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">讲解要点</text>
  <text x="160" y="775" font-family="Microsoft YaHei" font-size="28" fill="#374151">这是最基础、最刚需的部分！很多客户（尤其是子女）首先关注的就是这个。</text>
  <text x="160" y="805" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">介绍时要突出及时性、可靠性。结合本地成功预警案例（如有）。</text>
  
  <!-- 核心价值 -->
  <rect x="120" y="840" width="1680" height="120" rx="20" fill="#ef4444"/>
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">安全是基石，守护每一个瞬间</text>
  <text x="960" y="920" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="white">让老人安心，让子女放心，让家庭温心</text>
  <text x="960" y="950" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#fecaca">Safety First, Technology Cares</text>
  
  <!-- 成功案例提示 -->
  <rect x="120" y="980" width="1680" height="80" rx="15" fill="#10b981"/>
  <text x="960" y="1010" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">真实案例：某小区张奶奶燃气泄漏，系统2分钟内预警，</text>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">物业及时处理，避免了重大安全事故</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(80,350)">
    <circle cx="0" cy="0" r="25" fill="#ef4444" opacity="0.2"/>
    <path d="M-10,-10 L10,10 M10,-10 L-10,10" stroke="#ef4444" stroke-width="3"/>
    <circle cx="0" cy="0" r="8" fill="#ef4444"/>
  </g>
  
  <g transform="translate(1840,350)">
    <circle cx="0" cy="0" r="25" fill="#3b82f6" opacity="0.2"/>
    <path d="M-12,-8 L0,0 L12,-8 M0,0 L0,12" stroke="#3b82f6" stroke-width="3" fill="none"/>
  </g>
</svg>
