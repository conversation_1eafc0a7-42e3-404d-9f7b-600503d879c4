<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#bfdbfe" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">营销罗盘：4P+S 指引方向</text>
  
  <!-- 4P+S模型图 -->
  <g transform="translate(960,400)">
    <!-- 中心圆 -->
    <circle cx="0" cy="0" r="100" fill="#ef4444" opacity="0.9"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">营销</text>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">组合</text>
    
    <!-- Product -->
    <g transform="translate(-250,-200)">
      <circle cx="0" cy="0" r="80" fill="#10b981"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">Product</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">产品</text>
      <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">满足需求的"武器"</text>
      <text x="0" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">场景化定制</text>
      <text x="0" y="175" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">服务包设计</text>
    </g>
    
    <!-- Price -->
    <g transform="translate(250,-200)">
      <circle cx="0" cy="0" r="80" fill="#f59e0b"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">Price</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">价格</text>
      <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#92400e">体现价值的"标签"</text>
      <text x="0" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">价值定价</text>
      <text x="0" y="175" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">套餐模式</text>
    </g>
    
    <!-- Place -->
    <g transform="translate(-250,200)">
      <circle cx="0" cy="0" r="80" fill="#3b82f6"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">Place</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">渠道</text>
      <text x="0" y="-150" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">触达客户的"通路"</text>
      <text x="0" y="-120" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">直销+渠道</text>
      <text x="0" y="-95" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">厅店+社区</text>
    </g>
    
    <!-- Promotion -->
    <g transform="translate(250,200)">
      <circle cx="0" cy="0" r="80" fill="#8b5cf6"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">Promotion</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">推广</text>
      <text x="0" y="-150" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#6b21a8">告知价值的"声音"</text>
      <text x="0" y="-120" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">标杆案例</text>
      <text x="0" y="-95" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#374151">内容营销</text>
    </g>
    
    <!-- Service (特殊突出) -->
    <g transform="translate(0,280)">
      <circle cx="0" cy="0" r="100" fill="#ef4444" stroke="#fbbf24" stroke-width="6"/>
      <text x="0" y="-15" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">Service</text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">服务</text>
      <text x="0" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ef4444">赢得信任的"灵魂"！</text>
      <text x="0" y="185" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#374151">7x24响应</text>
      <text x="0" y="215" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#374151">主动关怀</text>
      <!-- 特殊光环效果 -->
      <circle cx="0" cy="0" r="120" fill="none" stroke="#fbbf24" stroke-width="3" stroke-dasharray="10,5" opacity="0.7"/>
      <circle cx="0" cy="0" r="140" fill="none" stroke="#fbbf24" stroke-width="2" stroke-dasharray="5,5" opacity="0.5"/>
    </g>
    
    <!-- 连接线 -->
    <path d="M-180,-140 L-70,-70" stroke="#6b7280" stroke-width="2" stroke-dasharray="3,3"/>
    <path d="M180,-140 L70,-70" stroke="#6b7280" stroke-width="2" stroke-dasharray="3,3"/>
    <path d="M-180,140 L-70,70" stroke="#6b7280" stroke-width="2" stroke-dasharray="3,3"/>
    <path d="M180,140 L70,70" stroke="#6b7280" stroke-width="2" stroke-dasharray="3,3"/>
    <path d="M0,100 L0,180" stroke="#ef4444" stroke-width="4"/>
  </g>
  
  <!-- 讲解要点 -->
  <rect x="120" y="850" width="1680" height="120" rx="20" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="890" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">讲解要点</text>
  <text x="160" y="925" font-family="Microsoft YaHei" font-size="28" fill="#374151">"这是一个经典的营销框架，但在智慧康养领域，</text>
  <text x="160" y="955" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">服务(Service)的重要性尤为突出，是我们建立差异化优势的关键！"</text>
  
  <!-- 核心理念 -->
  <rect x="120" y="990" width="1680" height="80" rx="15" fill="#3b82f6"/>
  <text x="960" y="1025" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">4P+S组合制胜，服务是灵魂</text>
  <text x="960" y="1055" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#dbeafe">Classic Framework, Service Excellence</text>
</svg>
