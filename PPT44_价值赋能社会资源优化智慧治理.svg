<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fef2f2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="govGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#fecaca" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">赋能④ 社会：资源优化，智慧治理</text>
  
  <!-- 政府/社会治理场景图 -->
  <g transform="translate(960,300)">
    <circle cx="0" cy="0" r="150" fill="url(#govGradient)" stroke="#ef4444" stroke-width="3"/>
    <!-- 城市建筑群 -->
    <rect x="-80" y="20" width="20" height="60" rx="3" fill="#ef4444"/>
    <rect x="-50" y="10" width="25" height="70" rx="3" fill="#dc2626"/>
    <rect x="-15" y="25" width="18" height="55" rx="3" fill="#ef4444"/>
    <rect x="15" y="5" width="22" height="75" rx="3" fill="#dc2626"/>
    <rect x="50" y="30" width="20" height="50" rx="3" fill="#ef4444"/>
    <!-- 智慧连接线 -->
    <path d="M-60,0 Q0,-30 60,0" stroke="#fbbf24" stroke-width="3" fill="none"/>
    <path d="M-40,0 Q0,-20 40,0" stroke="#fbbf24" stroke-width="2" fill="none"/>
    <path d="M-20,0 Q0,-10 20,0" stroke="#fbbf24" stroke-width="2" fill="none"/>
    <!-- 数据节点 -->
    <circle cx="-60" cy="0" r="5" fill="#fbbf24"/>
    <circle cx="-20" cy="0" r="5" fill="#fbbf24"/>
    <circle cx="20" cy="0" r="5" fill="#fbbf24"/>
    <circle cx="60" cy="0" r="5" fill="#fbbf24"/>
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#dc2626">智慧城市</text>
  </g>
  
  <!-- 从更高层面阐述价值 -->
  <text x="120" y="500" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#dc2626">社会价值五大维度</text>
  
  <!-- 优化养老资源配置 -->
  <rect x="120" y="530" width="1680" height="100" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="200" cy="570" r="30" fill="#10b981"/>
  <rect x="185" y="555" width="30" height="30" rx="5" fill="white"/>
  <circle cx="195" cy="565" r="3" fill="#10b981"/>
  <circle cx="205" cy="565" r="3" fill="#10b981"/>
  <circle cx="195" cy="575" r="3" fill="#10b981"/>
  <circle cx="205" cy="575" r="3" fill="#10b981"/>
  <rect x="190" y="580" width="20" height="5" rx="1" fill="#10b981"/>
  
  <text x="270" y="565" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">优化养老资源配置</text>
  <text x="270" y="595" font-family="Microsoft YaHei" font-size="26" fill="#374151">通过数据分析，实现养老资源的精准投放和高效利用</text>
  <text x="1200" y="580" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">让每一份投入都发挥最大效益</text>
  
  <!-- 提升基层治理效能 -->
  <rect x="120" y="650" width="1680" height="100" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="200" cy="690" r="30" fill="#3b82f6"/>
  <rect x="185" y="675" width="30" height="30" rx="5" fill="white"/>
  <path d="M190,680 L210,680 M190,690 L210,690 M190,700 L210,700" stroke="#3b82f6" stroke-width="2"/>
  
  <text x="270" y="685" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">提升基层治理效能</text>
  <text x="270" y="715" font-family="Microsoft YaHei" font-size="26" fill="#374151">数字化平台助力社区管理，提升服务响应速度和质量</text>
  <text x="1200" y="700" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">让治理更精准、更高效</text>
  
  <!-- 助力智慧城市建设 -->
  <rect x="120" y="770" width="1680" height="100" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="200" cy="810" r="30" fill="#f59e0b"/>
  <circle cx="200" cy="810" r="20" fill="white"/>
  <circle cx="200" cy="810" r="10" fill="#f59e0b"/>
  <path d="M190,800 Q200,790 210,800 M190,820 Q200,830 210,820" stroke="white" stroke-width="2" fill="none"/>
  
  <text x="270" y="805" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">助力智慧城市建设</text>
  <text x="270" y="835" font-family="Microsoft YaHei" font-size="26" fill="#374151">康养数据融入城市大脑，为智慧城市建设贡献重要数据支撑</text>
  <text x="1200" y="820" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">构建数字化城市生态</text>
  
  <!-- 分担社会养老压力 -->
  <rect x="120" y="890" width="800" height="80" rx="20" fill="white" stroke="#8b5cf6" stroke-width="3"/>
  <circle cx="200" cy="920" r="25" fill="#8b5cf6"/>
  <path d="M185,905 Q200,890 215,905 Q200,935 185,905" fill="white"/>
  <circle cx="200" cy="920" r="15" fill="#8b5cf6" opacity="0.3"/>
  
  <text x="270" y="915" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#6b21a8">分担社会养老压力</text>
  <text x="270" y="945" font-family="Microsoft YaHei" font-size="24" fill="#374151">科技赋能减轻家庭和社会养老负担</text>
  <text x="270" y="965" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">让老龄化社会更从容</text>
  
  <!-- 促进康养产业发展 -->
  <rect x="1000" y="890" width="800" height="80" rx="20" fill="white" stroke="#ef4444" stroke-width="3"/>
  <circle cx="1080" cy="920" r="25" fill="#ef4444"/>
  <rect x="1065" y="905" width="30" height="30" rx="5" fill="white"/>
  <path d="M1070,910 L1090,910 M1070,920 L1090,920 M1070,930 L1090,930" stroke="#ef4444" stroke-width="2"/>
  <circle cx="1080" cy="920" r="8" fill="#ef4444"/>
  
  <text x="1150" y="915" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#dc2626">促进康养产业发展</text>
  <text x="1150" y="945" font-family="Microsoft YaHei" font-size="24" fill="#374151">带动上下游产业链发展</text>
  <text x="1150" y="965" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">创造新的经济增长点</text>
  
  <!-- 社会价值总结 -->
  <rect x="120" y="990" width="1680" height="80" rx="20" fill="#dc2626"/>
  <text x="960" y="1020" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">构建智慧康养生态，助力美好社会建设</text>
  <text x="960" y="1055" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#fecaca">让科技成为社会进步的强大引擎</text>
  
  <!-- 讲师备注提示 -->
  <rect x="1400" y="530" width="400" height="120" rx="15" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <text x="1420" y="560" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#dc2626">讲师备注</text>
  <text x="1420" y="590" font-family="Microsoft YaHei" font-size="20" fill="#374151">这部分内容在对接</text>
  <text x="1420" y="615" font-family="Microsoft YaHei" font-size="20" fill="#374151">政府客户时尤为重要</text>
  <text x="1420" y="640" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">突出社会价值和意义</text>
</svg>
