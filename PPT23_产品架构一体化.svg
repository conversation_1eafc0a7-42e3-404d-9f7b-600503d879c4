<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#bfdbfe" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">"云网+平台+终端+应用+服务" 一体化架构</text>
  
  <!-- 架构层次图 -->
  <!-- 基础层：电信核心 -->
  <rect x="120" y="200" width="1680" height="120" rx="20" fill="#1e3a8a" opacity="0.1" stroke="#1e3a8a" stroke-width="3"/>
  <text x="160" y="230" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">基础 (电信核心)</text>
  
  <rect x="160" y="250" width="200" height="60" rx="10" fill="#3b82f6"/>
  <text x="260" y="285" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">云计算(天翼云)</text>
  
  <rect x="380" y="250" width="150" height="60" rx="10" fill="#10b981"/>
  <text x="455" y="285" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">大数据</text>
  
  <rect x="550" y="250" width="150" height="60" rx="10" fill="#f59e0b"/>
  <text x="625" y="285" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">AI平台</text>
  
  <rect x="720" y="250" width="200" height="60" rx="10" fill="#8b5cf6"/>
  <text x="820" y="275" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">泛在网络</text>
  <text x="820" y="295" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">(5G/NB/光网)</text>
  
  <rect x="940" y="250" width="180" height="60" rx="10" fill="#ef4444"/>
  <text x="1030" y="285" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">物联网平台</text>
  
  <rect x="1140" y="250" width="150" height="60" rx="10" fill="#06b6d4"/>
  <text x="1215" y="285" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">安全体系</text>
  
  <!-- 核心层：能力中枢 -->
  <rect x="120" y="350" width="1680" height="120" rx="20" fill="#10b981" opacity="0.1" stroke="#10b981" stroke-width="3"/>
  <text x="160" y="380" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">核心 (能力中枢)</text>
  
  <rect x="300" y="400" width="1320" height="60" rx="15" fill="#10b981"/>
  <text x="960" y="425" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">智慧康养 PaaS/SaaS 平台</text>
  <text x="960" y="450" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="white">连接管理 | 数据处理 | 能力开放 | 应用支撑</text>
  
  <!-- 前端层：用户触点 -->
  <rect x="120" y="500" width="1680" height="120" rx="20" fill="#3b82f6" opacity="0.1" stroke="#3b82f6" stroke-width="3"/>
  <text x="160" y="530" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">前端 (用户触点)</text>
  
  <rect x="200" y="550" width="300" height="60" rx="10" fill="#3b82f6"/>
  <text x="350" y="575" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">智能终端</text>
  <text x="350" y="595" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">(传感器/穿戴/家居等)</text>
  
  <rect x="520" y="550" width="300" height="60" rx="10" fill="#8b5cf6"/>
  <text x="670" y="575" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">应用入口</text>
  <text x="670" y="595" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">(APP/小程序/电视/音箱)</text>
  
  <!-- 支撑层：服务保障 -->
  <rect x="120" y="650" width="1680" height="120" rx="20" fill="#f59e0b" opacity="0.1" stroke="#f59e0b" stroke-width="3"/>
  <text x="160" y="680" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">支撑 (服务保障)</text>
  
  <rect x="200" y="700" width="180" height="60" rx="10" fill="#f59e0b"/>
  <text x="290" y="735" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">专业安装</text>
  
  <rect x="400" y="700" width="180" height="60" rx="10" fill="#ef4444"/>
  <text x="490" y="735" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">7x24客服</text>
  
  <rect x="600" y="700" width="180" height="60" rx="10" fill="#10b981"/>
  <text x="690" y="735" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">应急响应</text>
  
  <rect x="800" y="700" width="180" height="60" rx="10" fill="#8b5cf6"/>
  <text x="890" y="735" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">运维巡检</text>
  
  <!-- 连接箭头 -->
  <path d="M960,320 L960,350" stroke="#6b7280" stroke-width="4" marker-end="url(#arrowhead)"/>
  <path d="M960,470 L960,500" stroke="#6b7280" stroke-width="4" marker-end="url(#arrowhead)"/>
  <path d="M960,620 L960,650" stroke="#6b7280" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280"/>
    </marker>
  </defs>
  
  <!-- 讲解要点 -->
  <rect x="120" y="800" width="1680" height="120" rx="20" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="840" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">讲解要点</text>
  <text x="160" y="875" font-family="Microsoft YaHei" font-size="28" fill="#374151">我们提供的是一个端到端的完整解决方案。最底层的云网能力是我们的核心优势，</text>
  <text x="160" y="905" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">中间的平台是大脑和枢纽，前端的设备和应用是触角，贯穿始终的服务是保障。</text>
  
  <!-- 整体价值 -->
  <rect x="120" y="940" width="1680" height="80" rx="15" fill="#10b981"/>
  <text x="960" y="970" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">这是一个有机的整体</text>
  <text x="960" y="1000" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="white">每一层都不可或缺，共同构建智慧康养生态</text>
  
  <!-- 装饰元素 -->
  <circle cx="80" cy="400" r="20" fill="#3b82f6" opacity="0.2"/>
  <circle cx="1840" cy="400" r="20" fill="#10b981" opacity="0.2"/>
  <circle cx="80" cy="700" r="20" fill="#f59e0b" opacity="0.2"/>
  <circle cx="1840" cy="700" r="20" fill="#ef4444" opacity="0.2"/>
</svg>
