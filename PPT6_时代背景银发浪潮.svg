<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:0.6" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#e5e7eb" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">数据说话：老龄化已是不可逆转的"新常态"</text>
  
  <!-- 核心数据图表区域 -->
  <rect x="120" y="200" width="800" height="400" rx="20" fill="white" stroke="#e5e7eb" stroke-width="2"/>
  <text x="140" y="240" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">中国老龄化趋势</text>
  
  <!-- 柱状图 -->
  <!-- 60+人口 -->
  <rect x="200" y="280" width="80" height="200" rx="5" fill="url(#chartGradient)"/>
  <text x="240" y="270" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1e3a8a">60+</text>
  <text x="240" y="510" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#dc2626">2.6亿</text>
  <text x="240" y="535" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">18.7%</text>
  
  <!-- 65+人口 -->
  <rect x="320" y="320" width="80" height="160" rx="5" fill="#10b981"/>
  <text x="360" y="310" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#065f46">65+</text>
  <text x="360" y="510" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#dc2626">2.0亿</text>
  <text x="360" y="535" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">14.2%</text>
  
  <!-- 80+人口 -->
  <rect x="440" y="400" width="80" height="80" rx="5" fill="#f59e0b"/>
  <text x="480" y="390" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#92400e">80+</text>
  <text x="480" y="510" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#dc2626">0.35亿</text>
  <text x="480" y="535" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">2.5%</text>
  
  <!-- 趋势箭头 -->
  <path d="M580,350 L680,300" stroke="#ef4444" stroke-width="4" marker-end="url(#arrowhead)"/>
  <text x="630" y="315" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ef4444">快速增长</text>
  
  <!-- 预测数据 -->
  <text x="140" y="580" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">未来预测：</text>
  <text x="280" y="580" font-family="Microsoft YaHei" font-size="26" fill="#374151">2035年进入重度老龄化（60+超30%）</text>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ef4444"/>
    </marker>
  </defs>
  
  <!-- 结构特点 -->
  <text x="1000" y="240" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">结构特点</text>
  
  <!-- 特点1：高龄化 -->
  <rect x="1000" y="270" width="800" height="100" rx="15" fill="#fef2f2" stroke="#ef4444" stroke-width="2"/>
  <circle cx="1060" cy="320" r="30" fill="#ef4444"/>
  <text x="1060" y="330" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">高</text>
  <text x="1120" y="305" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#dc2626">高龄化</text>
  <text x="1120" y="335" font-family="Microsoft YaHei" font-size="24" fill="#374151">80岁以上增速快 → 照护需求更复杂</text>
  <text x="1120" y="360" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">专业护理、医疗服务需求激增</text>
  
  <!-- 特点2：空巢化 -->
  <rect x="1000" y="390" width="800" height="100" rx="15" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
  <circle cx="1060" cy="440" r="30" fill="#f59e0b"/>
  <text x="1060" y="450" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">空</text>
  <text x="1120" y="425" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#92400e">空巢/独居化</text>
  <text x="1120" y="455" font-family="Microsoft YaHei" font-size="24" fill="#374151">超1亿 → 安全、精神慰藉需求突出</text>
  <text x="1120" y="480" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">智能监护、情感陪伴成刚需</text>
  
  <!-- 特点3：失能化 -->
  <rect x="1000" y="510" width="800" height="100" rx="15" fill="#ecfdf5" stroke="#10b981" stroke-width="2"/>
  <circle cx="1060" cy="560" r="30" fill="#10b981"/>
  <text x="1060" y="570" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">护</text>
  <text x="1120" y="545" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#065f46">失能/半失能化</text>
  <text x="1120" y="575" font-family="Microsoft YaHei" font-size="24" fill="#374151">超4000万 → 专业护理、康复需求巨大</text>
  <text x="1120" y="600" font-family="Microsoft YaHei" font-size="22" fill="#6b7280">智能辅助、远程医疗市场广阔</text>
  
  <!-- 重点强调 -->
  <rect x="120" y="650" width="1680" height="120" rx="20" fill="#1e3a8a" opacity="0.1"/>
  <text x="160" y="690" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">讲解要点</text>
  <text x="160" y="730" font-family="Microsoft YaHei" font-size="28" fill="#374151">大家看到的这些数字，不仅仅是统计数据，背后是</text>
  <text x="160" y="760" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">亿万家庭面临的现实挑战，更是我们智慧康养业务的广阔市场基础！</text>
  
  <!-- 市场规模强调 -->
  <rect x="120" y="800" width="1680" height="80" rx="15" fill="#dc2626"/>
  <text x="960" y="850" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">这个市场规模巨大，而且需求是刚性的！</text>
  
  <!-- 装饰元素 -->
  <circle cx="1700" cy="350" r="40" fill="#3b82f6" opacity="0.1"/>
  <circle cx="1700" cy="350" r="20" fill="#3b82f6" opacity="0.2"/>
  <path d="M1685,335 L1685,365 M1700,320 L1700,380 M1715,335 L1715,365" stroke="#3b82f6" stroke-width="3"/>
</svg>
