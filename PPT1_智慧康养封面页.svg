<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,200 Q480,150 960,200 T1920,200" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 中国电信Logo区域 -->
  <rect x="120" y="80" width="200" height="80" rx="10" fill="#1e3a8a" opacity="0.1"/>
  <text x="220" y="135" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">中国电信</text>
  
  <!-- 主标题 -->
  <text x="960" y="380" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="url(#titleGradient)">智慧领航，银龄共赢</text>
  
  <!-- 副标题1 -->
  <text x="960" y="460" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#374151">智慧康养市场攻坚实战营</text>
  
  <!-- Day 1标识 -->
  <rect x="820" y="500" width="280" height="60" rx="30" fill="#3b82f6"/>
  <text x="960" y="540" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">Day 1</text>
  
  <!-- 副标题2 -->
  <text x="960" y="640" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#6b7280">聚力识局，智启银龄 - 理论赋能篇</text>
  
  <!-- 讲师信息区域 -->
  <rect x="120" y="850" width="400" height="150" rx="15" fill="white" stroke="#e5e7eb" stroke-width="2"/>
  <text x="140" y="890" font-family="Microsoft YaHei" font-size="28" fill="#374151">讲师：讲师姓名</text>
  <text x="140" y="930" font-family="Microsoft YaHei" font-size="28" fill="#374151">部门：相关部门</text>
  <text x="140" y="970" font-family="Microsoft YaHei" font-size="28" fill="#374151">日期：XXXX年XX月XX日</text>
  
  <!-- 装饰图标 -->
  <circle cx="1600" cy="300" r="80" fill="#3b82f6" opacity="0.1"/>
  <circle cx="1600" cy="300" r="40" fill="#3b82f6" opacity="0.2"/>
  <path d="M1580,280 L1580,320 M1600,260 L1600,340 M1620,280 L1620,320" stroke="#3b82f6" stroke-width="4" stroke-linecap="round"/>
  
  <circle cx="320" cy="750" r="60" fill="#10b981" opacity="0.1"/>
  <circle cx="320" cy="750" r="30" fill="#10b981" opacity="0.2"/>
  <path d="M305,735 L320,750 L335,735 M320,750 L320,765" stroke="#10b981" stroke-width="3" stroke-linecap="round" fill="none"/>
</svg>
