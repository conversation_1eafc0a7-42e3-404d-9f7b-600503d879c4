<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0fdf4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#065f46;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="moduleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#bbf7d0" stroke-width="3" fill="none"/>
  
  <!-- 模块标识 -->
  <rect x="120" y="80" width="200" height="60" rx="30" fill="#10b981"/>
  <text x="220" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">模块三</text>
  
  <!-- 标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">模块三：读懂"他/她"心 - 客户分析与需求挖掘</text>
  
  <!-- 模块目标 -->
  <rect x="120" y="230" width="1680" height="150" rx="25" fill="url(#moduleGradient)" stroke="#10b981" stroke-width="3"/>
  <text x="160" y="280" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#065f46">模块目标</text>
  <text x="160" y="320" font-family="Microsoft YaHei" font-size="32" fill="#374151">"了解了产品，我们来看客户。如何找到对的人？如何问出他们的真实需求？</text>
  <text x="160" y="355" font-family="Microsoft YaHei" font-size="32" fill="#374151" font-weight="bold">这一章，我们学习'看透'客户的工具和方法。"</text>
  
  <!-- 核心议题 -->
  <text x="120" y="430" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#065f46">核心议题</text>
  
  <!-- 客户细分 -->
  <rect x="120" y="460" width="520" height="180" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="220" cy="530" r="40" fill="#3b82f6"/>
  <circle cx="220" cy="530" r="25" fill="white"/>
  <circle cx="220" cy="530" r="12" fill="#3b82f6"/>
  <path d="M210,520 L230,520 M210,530 L230,530 M210,540 L230,540" stroke="white" stroke-width="2"/>
  
  <text x="300" y="510" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">客户细分</text>
  <text x="300" y="550" font-family="Microsoft YaHei" font-size="28" fill="#374151">STP理论应用</text>
  <text x="300" y="580" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">找准目标客户群</text>
  <text x="300" y="605" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">B端/C端差异化分析</text>
  
  <!-- 客户画像 -->
  <rect x="700" y="460" width="520" height="180" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="800" cy="530" r="40" fill="#f59e0b"/>
  <circle cx="800" cy="515" r="15" fill="white"/>
  <circle cx="795" cy="510" r="2" fill="#f59e0b"/>
  <circle cx="805" cy="510" r="2" fill="#f59e0b"/>
  <path d="M790,520 Q800,525 810,520" stroke="#f59e0b" stroke-width="2" fill="none"/>
  <ellipse cx="800" cy="545" rx="20" ry="12" fill="white"/>
  
  <text x="880" y="510" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">客户画像</text>
  <text x="880" y="550" font-family="Microsoft YaHei" font-size="28" fill="#374151">Persona工具</text>
  <text x="880" y="580" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">让客户"活"起来</text>
  <text x="880" y="605" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">痛点与需求分析</text>
  
  <!-- SPIN需求挖掘法 -->
  <rect x="1280" y="460" width="520" height="180" rx="20" fill="white" stroke="#ef4444" stroke-width="3"/>
  <circle cx="1380" cy="530" r="40" fill="#ef4444"/>
  <text x="1380" y="540" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">SPIN</text>
  
  <text x="1460" y="510" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#dc2626">SPIN需求挖掘法</text>
  <text x="1460" y="550" font-family="Microsoft YaHei" font-size="28" fill="#374151">结构化提问艺术</text>
  <text x="1460" y="580" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">挖掘深层需求</text>
  <text x="1460" y="605" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">顾问式销售核心</text>
  
  <!-- 学习路径 -->
  <g transform="translate(960,720)">
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#065f46">学习路径</text>
    
    <!-- 步骤1 -->
    <rect x="-400" y="0" width="150" height="80" rx="15" fill="#3b82f6"/>
    <text x="-325" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">第一步</text>
    <text x="-325" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">理解细分</text>
    <text x="-325" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">找准目标</text>
    
    <!-- 箭头1 -->
    <path d="M-230,40 L-180,40" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 步骤2 -->
    <rect x="-150" y="0" width="150" height="80" rx="15" fill="#f59e0b"/>
    <text x="-75" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">第二步</text>
    <text x="-75" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">绘制画像</text>
    <text x="-75" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">深度理解</text>
    
    <!-- 箭头2 -->
    <path d="M20,40 L70,40" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 步骤3 -->
    <rect x="100" y="0" width="150" height="80" rx="15" fill="#ef4444"/>
    <text x="175" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">第三步</text>
    <text x="175" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">SPIN提问</text>
    <text x="175" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">挖掘需求</text>
    
    <!-- 箭头3 -->
    <path d="M270,40 L320,40" stroke="#6b7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 结果 -->
    <rect x="350" y="0" width="150" height="80" rx="15" fill="#10b981"/>
    <text x="425" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">成果</text>
    <text x="425" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">精准沟通</text>
    <text x="425" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">高效成交</text>
  </g>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280"/>
    </marker>
  </defs>
  
  <!-- 核心理念 -->
  <rect x="120" y="850" width="1680" height="120" rx="20" fill="#10b981"/>
  <text x="960" y="890" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">知己知彼，百战不殆</text>
  <text x="960" y="930" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="white">读懂客户心声，才能精准击中需求</text>
  <text x="960" y="960" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#dcfce7">Know Your Customer, Win Their Heart</text>
  
  <!-- 学习提示 -->
  <rect x="120" y="990" width="1680" height="80" rx="15" fill="#065f46" opacity="0.1"/>
  <text x="160" y="1020" font-family="Microsoft YaHei" font-size="28" fill="#374151">本模块将通过理论讲解+案例分析+实战演练的方式，</text>
  <text x="160" y="1050" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">让大家掌握客户分析和需求挖掘的核心技能。</text>
</svg>
