<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fef3c7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#92400e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:0.6" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰弧线 -->
  <path d="M0,120 Q960,80 1920,120" stroke="#fde68a" stroke-width="3" fill="none"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#titleGradient)">"智家孝老"利器④：亲情关怀 (跨越距离)</text>
  
  <!-- 高清视频通话 -->
  <rect x="120" y="200" width="800" height="220" rx="20" fill="white" stroke="#3b82f6" stroke-width="3"/>
  <circle cx="220" cy="290" r="50" fill="#3b82f6"/>
  <rect x="195" y="265" width="50" height="50" rx="8" fill="white"/>
  <circle cx="210" cy="280" r="8" fill="#3b82f6"/>
  <circle cx="230" cy="280" r="8" fill="#3b82f6"/>
  <ellipse cx="220" cy="300" rx="15" ry="8" fill="#3b82f6"/>
  <path d="M205,270 Q220,260 235,270" stroke="#3b82f6" stroke-width="2" fill="none"/>
  
  <text x="300" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">高清视频通话</text>
  <text x="300" y="290" font-family="Microsoft YaHei" font-size="28" fill="#374151">电视/带屏音箱/APP</text>
  <text x="300" y="325" font-family="Microsoft YaHei" font-size="26" fill="#374151">大字体/简化操作</text>
  <text x="300" y="360" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#3b82f6">价值："想见就见，操作简单"</text>
  <text x="300" y="395" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">解决：老人与子女视频沟通障碍</text>
  
  <!-- 电子相册/留言 -->
  <rect x="1000" y="200" width="800" height="220" rx="20" fill="white" stroke="#10b981" stroke-width="3"/>
  <circle cx="1100" cy="290" r="50" fill="#10b981"/>
  <rect x="1075" y="265" width="50" height="50" rx="5" fill="white"/>
  <rect x="1080" y="270" width="15" height="12" rx="2" fill="#10b981"/>
  <rect x="1100" y="270" width="15" height="12" rx="2" fill="#10b981"/>
  <rect x="1080" y="287" width="15" height="12" rx="2" fill="#10b981"/>
  <rect x="1100" y="287" width="15" height="12" rx="2" fill="#10b981"/>
  <rect x="1080" y="304" width="35" height="8" rx="1" fill="#10b981"/>
  
  <text x="1180" y="250" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#065f46">电子相册/留言</text>
  <text x="1180" y="290" font-family="Microsoft YaHei" font-size="28" fill="#374151">照片分享/语音留言</text>
  <text x="1180" y="325" font-family="Microsoft YaHei" font-size="26" fill="#374151">解决：子女无法时刻陪伴</text>
  <text x="1180" y="360" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#10b981">价值："让爱随时在线"</text>
  <text x="1180" y="395" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">温馨回忆，情感连接</text>
  
  <!-- 远程提醒 -->
  <rect x="120" y="450" width="1680" height="150" rx="20" fill="white" stroke="#f59e0b" stroke-width="3"/>
  <circle cx="220" cy="525" r="50" fill="#f59e0b"/>
  <circle cx="220" cy="525" r="30" fill="white"/>
  <circle cx="220" cy="525" r="15" fill="#f59e0b"/>
  <path d="M210,515 L230,515 M220,505 L220,525 L230,535" stroke="white" stroke-width="3" fill="none"/>
  
  <text x="300" y="510" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#92400e">远程提醒</text>
  <text x="300" y="550" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#f59e0b">价值："温馨提醒，远程送达"</text>
  <text x="700" y="525" font-family="Microsoft YaHei" font-size="28" fill="#374151">解决：子女担心老人忘记事情</text>
  <text x="1200" y="525" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">吃药提醒、生日祝福、天气关怀</text>
  
  <!-- 亲情互动场景 -->
  <g transform="translate(960,650)">
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">亲情互动场景</text>
    
    <!-- 视频通话场景 -->
    <rect x="-400" y="0" width="200" height="100" rx="15" fill="#3b82f6" opacity="0.1" stroke="#3b82f6" stroke-width="2"/>
    <text x="-300" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1e3a8a">视频通话</text>
    <text x="-300" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">每日问候</text>
    <text x="-300" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">节日祝福</text>
    <text x="-300" y="95" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">孙辈互动</text>
    
    <!-- 照片分享场景 -->
    <rect x="-150" y="0" width="200" height="100" rx="15" fill="#10b981" opacity="0.1" stroke="#10b981" stroke-width="2"/>
    <text x="-50" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#065f46">照片分享</text>
    <text x="-50" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">生活点滴</text>
    <text x="-50" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">旅游见闻</text>
    <text x="-50" y="95" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">家庭聚会</text>
    
    <!-- 语音留言场景 -->
    <rect x="100" y="0" width="200" height="100" rx="15" fill="#f59e0b" opacity="0.1" stroke="#f59e0b" stroke-width="2"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#92400e">语音留言</text>
    <text x="200" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">工作忙碌时</text>
    <text x="200" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">出差在外时</text>
    <text x="200" y="95" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#374151">深夜关怀</text>
  </g>
  
  <!-- 温暖的心 -->
  <g transform="translate(960,800)">
    <path d="M-40,0 Q-60,-20 -80,0 Q-60,40 0,80 Q60,40 80,0 Q60,-20 40,0 Q20,-20 0,0 Q-20,-20 -40,0" fill="url(#heartGradient)"/>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">情感</text>
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">连接</text>
  </g>
  
  <!-- 讲解要点 -->
  <rect x="120" y="900" width="1680" height="80" rx="15" fill="#92400e" opacity="0.1"/>
  <text x="160" y="930" font-family="Microsoft YaHei" font-size="28" fill="#374151">情感关怀是打动客户（尤其是子女）的重要方面。</text>
  <text x="160" y="960" font-family="Microsoft YaHei" font-size="28" fill="#374151" font-weight="bold">强调技术让亲情更近，让关爱无距离。</text>
  
  <!-- 价值强调 -->
  <rect x="120" y="1000" width="1680" height="70" rx="15" fill="#dc2626"/>
  <text x="960" y="1030" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">跨越距离的爱，让每一份关怀都能及时传达</text>
  <text x="960" y="1055" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">Love Knows No Distance</text>
</svg>
